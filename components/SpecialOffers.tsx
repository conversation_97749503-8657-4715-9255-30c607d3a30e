import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { memo, useCallback, useEffect, useRef } from 'react';
import { Animated, Dimensions, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Colors from '../constants/Colors';
import ShadowCard from './ui/ShadowCard';

// Define types for offers
export interface Offer {
  id: string;
  title: string;
  description: string;
  colors: [string, string];
  icon: keyof typeof FontAwesome.glyphMap;
}

interface SpecialOffersProps {
  onOfferPress: (offer: Offer) => void;
}

const { width } = Dimensions.get('window');
const IS_WEB = Platform.OS === 'web';

// Define offers data
const OFFERS: Offer[] = [
  {
    id: '1',
    title: '50% OFF',
    description: 'First order',
    colors: Colors.offers.first as [string, string],
    icon: 'tag',
  },
  {
    id: '2',
    title: 'Free Delivery',
    description: 'Above ₹299',
    colors: Colors.offers.delivery as [string, string],
    icon: 'truck',
  },
  {
    id: '3',
    title: 'Cashback',
    description: 'Up to ₹100',
    colors: ['#8E44AD', '#9B59B6'] as [string, string],
    icon: 'money',
  },
];

// Memoized OfferCard component
const OfferCard = memo(({ 
  offer, 
  index, 
  scrollX, 
  onPress 
}: { 
  offer: Offer; 
  index: number; 
  scrollX: Animated.Value;
  onPress: (offer: Offer) => void;
}) => {
  const inputRange = [
    (index - 1) * (width * 0.75 + 16),
    index * (width * 0.75 + 16),
    (index + 1) * (width * 0.75 + 16),
  ];
  
  const scale = scrollX.interpolate({
    inputRange,
    outputRange: [0.9, 1, 0.9],
    extrapolate: 'clamp',
  });
  
  const opacity = scrollX.interpolate({
    inputRange,
    outputRange: [0.7, 1, 0.7],
    extrapolate: 'clamp',
  });

  const handlePress = useCallback(() => {
    onPress(offer);
  }, [offer, onPress]);
  
  return (
    <Animated.View 
      key={offer.id}
      style={[
        styles.animatedCard,
        {
          transform: [{ scale }],
          opacity,
        }
      ]}
    >
      <TouchableOpacity
        style={styles.offerCard}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <ShadowCard depth="medium" style={styles.shadowCard} borderRadius={16}>
          <LinearGradient
            colors={offer.colors}
            style={styles.gradientCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {/* Decorative circles */}
            <View style={styles.decorCircle1} />
            <View style={styles.decorCircle2} />
            
            <View style={styles.iconContainer}>
              <FontAwesome name={offer.icon} size={24} color="#FFFFFF" />
            </View>
            <View style={styles.offerContent}>
              <Text style={styles.offerTitle}>{offer.title}</Text>
              <Text style={styles.offerDescription}>{offer.description}</Text>
            </View>
          </LinearGradient>
        </ShadowCard>
      </TouchableOpacity>
    </Animated.View>
  );
});

// Memoized pagination dot component
const PaginationDot = memo(({ 
  index, 
  scrollX 
}: { 
  index: number;
  scrollX: Animated.Value;
}) => {
  const inputRange = [
    (index - 1) * (width * 0.75 + 16),
    index * (width * 0.75 + 16),
    (index + 1) * (width * 0.75 + 16),
  ];
  
  const dotWidth = scrollX.interpolate({
    inputRange,
    outputRange: [8, 24, 8],
    extrapolate: 'clamp',
  });
  
  const dotOpacity = scrollX.interpolate({
    inputRange,
    outputRange: [0.4, 1, 0.4],
    extrapolate: 'clamp',
  });
  
  return (
    <Animated.View 
      key={index} 
      style={[
        styles.dot,
        {
          width: dotWidth,
          opacity: dotOpacity,
          backgroundColor: Colors.primary.end,
        }
      ]}
    />
  );
});

export const SpecialOffers: React.FC<SpecialOffersProps> = ({ onOfferPress }) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const titleAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    Animated.timing(titleAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: !IS_WEB,
    }).start();
  }, []);

  // Memoized event handler for scroll
  const handleScroll = useCallback(
    Animated.event(
      [{ nativeEvent: { contentOffset: { x: scrollX } } }],
      { useNativeDriver: !IS_WEB }
    ),
    []
  );

  return (
    <View style={styles.container}>
      <Animated.Text 
        style={[
          styles.sectionTitle,
          {
            opacity: titleAnim,
            transform: [
              { translateY: titleAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}
            ]
          }
        ]}
      >
        Special Offers
      </Animated.Text>
      
      <Animated.ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        snapToInterval={width * 0.75 + 16}
        decelerationRate="fast"
        onScroll={handleScroll}
      >
        {OFFERS.map((offer, index) => (
          <OfferCard 
            key={offer.id}
            offer={offer} 
            index={index} 
            scrollX={scrollX} 
            onPress={onOfferPress}
          />
        ))}
      </Animated.ScrollView>
      
      {/* Pagination dots */}
      <View style={styles.pagination}>
        {OFFERS.map((_, index) => (
          <PaginationDot key={index} index={index} scrollX={scrollX} />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
    backgroundColor: Colors.background,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    paddingHorizontal: 20,
    color: Colors.text,
  },
  scrollContent: {
    paddingHorizontal: 10,
    paddingBottom: 16,
  },
  animatedCard: {
    width: width * 0.75,
    marginHorizontal: 8,
  },
  offerCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  shadowCard: {
    borderRadius: 16,
  },
  gradientCard: {
    padding: 20,
    height: 120,
    borderRadius: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  offerContent: {
    flex: 1,
  },
  offerTitle: {
    color: '#FFFFFF',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
    ...createTextShadow({
      textShadowColor: 'rgba(0, 0, 0, 0.1)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 3,
    }),
  },
  offerDescription: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.9,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  dot: {
    height: 8,
    width: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  decorCircle1: {
    position: 'absolute',
    top: -20,
    right: -20,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  decorCircle2: {
    position: 'absolute',
    bottom: -40,
    left: -10,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
});

export default memo(SpecialOffers); 