import { FontAwesome } from '@expo/vector-icons';
import React, { memo, useEffect, useRef } from 'react';
import { Animated, Dimensions, Platform, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import Colors from '../constants/Colors';
import useAnimations from '../hooks/useAnimations';
import ShadowCard from './ui/ShadowCard';

interface SearchBarProps {
  onPress?: () => void;
  focused?: boolean;
}

const SCREEN_WIDTH = Dimensions.get('window').width;
const IS_WEB = Platform.OS === 'web';

export const SearchBar: React.FC<SearchBarProps> = memo(({ onPress, focused = false }) => {
  const { slideUpAnimation, opacityAnimation } = useAnimations();
  const animatedWidth = useRef(new Animated.Value(SCREEN_WIDTH - 32)).current;
  const animatedElevation = useRef(new Animated.Value(1)).current;
  
  useEffect(() => {
    // Initial entrance animation
    Animated.parallel([
      Animated.timing(slideUpAnimation, {
        toValue: 0,
        duration: 400,
        useNativeDriver: !IS_WEB,
      }),
      Animated.timing(opacityAnimation, {
        toValue: 1,
        duration: 400,
        useNativeDriver: !IS_WEB,
      })
    ]).start();
  }, []);
  
  useEffect(() => {
    // Animation when focus state changes
    Animated.parallel([
      Animated.spring(animatedWidth, {
        toValue: focused ? SCREEN_WIDTH - 24 : SCREEN_WIDTH - 32,
        useNativeDriver: false, // Layout animations can't use native driver
        friction: 8,
      }),
      Animated.timing(animatedElevation, {
        toValue: focused ? 4 : 1,
        duration: 200,
        useNativeDriver: false, // Layout animations can't use native driver
      })
    ]).start();
  }, [focused]);

  const shadowStyle = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: animatedElevation },
    shadowOpacity: Animated.multiply(animatedElevation, 0.05),
    shadowRadius: Animated.multiply(animatedElevation, 1),
    elevation: animatedElevation,
  };

  return (
    <Animated.View 
      style={[
        styles.container,
        { 
          transform: [{ translateY: slideUpAnimation }],
          opacity: opacityAnimation,
          width: animatedWidth,
        }
      ]}
    >
      <ShadowCard depth="low" style={styles.searchContainer} borderRadius={12}>
        <TouchableOpacity 
          style={styles.searchWrapper}
          onPress={onPress}
          activeOpacity={0.9}
        >
          <FontAwesome 
            name="search" 
            size={16} 
            color={focused ? Colors.primary.end : "#757575"} 
            style={styles.icon} 
          />
          <TextInput
            style={styles.input}
            placeholder="Search for groceries & essentials"
            placeholderTextColor="#757575"
            editable={false}
          />
        </TouchableOpacity>
      </ShadowCard>
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    alignSelf: 'center',
    marginHorizontal: 16,
    marginVertical: 16,
  },
  searchContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
  },
  searchWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  icon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 15,
    color: Colors.text,
    fontWeight: '500',
  },
});

export default SearchBar; 