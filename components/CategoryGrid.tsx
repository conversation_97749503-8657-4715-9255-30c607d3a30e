import { LinearGradient } from 'expo-linear-gradient';
import React, { memo, useCallback } from 'react';
import { Animated, FlatList, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Colors from '../constants/Colors';
import ShadowCard from './ui/ShadowCard';

// Define Category type explicitly
export interface Category {
  id: string;
  name: string;
  emoji: string;
  colors: [string, string];
}

interface CategoryGridProps {
  onCategoryPress: (category: Category) => void;
}

// Create empty array for categories to be filled from API in a real app
const CATEGORIES: Category[] = [
  { id: '1', name: 'Vegetables', emoji: '🥬', colors: Colors.categories.vegetables as [string, string] },
  { id: '2', name: 'Fruits', emoji: '🍎', colors: Colors.categories.fruits as [string, string] },
  { id: '3', name: 'Dairy', emoji: '🥛', colors: Colors.categories.dairy as [string, string] },
  { id: '4', name: 'Personal Care', emoji: '🧴', colors: Colors.categories.personalCare as [string, string] },
  { id: '5', name: 'Household', emoji: '🏠', colors: Colors.categories.household as [string, string] },
  { id: '6', name: 'Snacks', emoji: '🍪', colors: Colors.categories.snacks as [string, string] },
  { id: '7', name: 'Baby Care', emoji: '🍼', colors: Colors.categories.babyCare as [string, string] },
  { id: '8', name: 'Health', emoji: '💊', colors: Colors.categories.health as [string, string] },
];

// Memoize the CategoryItem component to prevent unnecessary re-renders
const CategoryItem = memo(({ 
  item, 
  index, 
  onPress 
}: { 
  item: Category; 
  index: number; 
  onPress: (item: Category) => void;
}) => {
  const IS_WEB = Platform.OS === 'web';
  // Create individual animated value for each category
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;
  
  // Create staggered entrance animation
  React.useEffect(() => {
    Animated.sequence([
      Animated.delay(index * 50), // Stagger the animations
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: !IS_WEB,
        friction: 8,
        tension: 40,
      })
    ]).start();
  }, [index]);

  const handlePress = useCallback(() => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: !IS_WEB,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 40,
        useNativeDriver: !IS_WEB,
      })
    ]).start(() => onPress(item));
  }, [item, onPress]);

  return (
    <Animated.View 
      style={[
        styles.animatedContainer,
        { 
          transform: [{ scale: scaleAnim }],
          opacity: scaleAnim.interpolate({
            inputRange: [0.7, 1],
            outputRange: [0.7, 1]
          })
        }
      ]}
    >
      <TouchableOpacity
        style={styles.categoryItem}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <ShadowCard depth="low" style={styles.categoryIconWrapper} borderRadius={30}>
          <LinearGradient
            colors={item.colors}
            style={styles.categoryIcon}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.emoji}>{item.emoji}</Text>
          </LinearGradient>
        </ShadowCard>
        <Text style={styles.categoryName}>{item.name}</Text>
      </TouchableOpacity>
    </Animated.View>
  );
});

export const CategoryGrid: React.FC<CategoryGridProps> = ({ onCategoryPress }) => {
  // Use memoized renderItem function for better performance
  const renderCategory = useCallback(({ item, index }: { item: Category; index: number }) => {
    return (
      <CategoryItem
        item={item}
        index={index}
        onPress={onCategoryPress}
      />
    );
  }, [onCategoryPress]);

  // Use key extractor callback for better performance
  const keyExtractor = useCallback((item: Category) => item.id, []);

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Categories</Text>
      <FlatList
        data={CATEGORIES}
        renderItem={renderCategory}
        keyExtractor={keyExtractor}
        numColumns={4}
        scrollEnabled={false}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true} // Improve performance for off-screen items
        initialNumToRender={8} // Only render visible items initially
        maxToRenderPerBatch={8} // Limit number of items rendered in each batch
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: Colors.background.primary,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    color: Colors.text,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  animatedContainer: {
    width: '22%',
  },
  categoryItem: {
    alignItems: 'center',
    width: '100%',
  },
  categoryIconWrapper: {
    marginBottom: 8,
    borderRadius: 30,
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emoji: {
    fontSize: 24,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    color: Colors.text,
    marginTop: 4,
  },
});

export default memo(CategoryGrid); 