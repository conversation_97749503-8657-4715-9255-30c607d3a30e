import React, { ReactNode } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import Colors from '../../constants/Colors';
import { createShadow } from '../../utils/shadows';

interface ShadowCardProps {
  children: ReactNode;
  style?: ViewStyle;
  depth?: 'low' | 'medium' | 'high';
  borderRadius?: number;
}

export const ShadowCard: React.FC<ShadowCardProps> = ({
  children,
  style,
  depth = 'medium',
  borderRadius = 12,
}) => {
  const getShadowStyle = () => {
    switch (depth) {
      case 'low':
        return createShadow({
          elevation: 2,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
        });
      case 'high':
        return createShadow({
          elevation: 8,
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.2,
          shadowRadius: 10,
        });
      case 'medium':
      default:
        return createShadow({
          elevation: 4,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 6,
        });
    }
  };

  return (
    <View
      style={[
        styles.container,
        getShadowStyle(),
        { borderRadius },
        style,
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    overflow: 'hidden',
  },
});

export default ShadowCard; 