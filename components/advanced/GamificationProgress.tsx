import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import * as Animatable from 'react-native-animatable';

interface GamificationProgressProps {
  type: 'level' | 'streak' | 'achievement' | 'points' | 'daily_challenge';
  data: any;
  onPress?: () => void;
  style?: any;
}

interface LevelData {
  currentLevel: number;
  currentXP: number;
  xpToNextLevel: number;
  levelName: string;
  nextLevelName: string;
  perks: string[];
}

interface StreakData {
  count: number;
  type: string;
  isActive: boolean;
  nextReward: number;
  maxStreak: number;
}

interface AchievementData {
  title: string;
  description: string;
  progress: number;
  maxProgress: number;
  points: number;
  icon: string;
  unlocked: boolean;
}

interface PointsData {
  current: number;
  earned: number;
  redeemable: number;
  nextReward: number;
}

interface DailyChallengeData {
  title: string;
  description: string;
  progress: number;
  target: number;
  reward: number;
  timeLeft: number;
  completed: boolean;
}

const { width } = Dimensions.get('window');

const GamificationProgress: React.FC<GamificationProgressProps> = ({
  type,
  data,
  onPress,
  style,
}) => {
  const [progressAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));
  const [glowAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Animate progress bar
    const progressValue = getProgressValue();
    Animated.timing(progressAnim, {
      toValue: progressValue,
      duration: 1500,
      useNativeDriver: false,
    }).start();

    // Pulse animation for completed items
    if (isCompleted()) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Glow effect for achievements
      Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: false,
          }),
          Animated.timing(glowAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: false,
          }),
        ])
      ).start();
    }
  }, [data]);

  const getProgressValue = (): number => {
    switch (type) {
      case 'level':
        const levelData = data as LevelData;
        return levelData.currentXP / levelData.xpToNextLevel;
      case 'achievement':
        const achievementData = data as AchievementData;
        return achievementData.progress / achievementData.maxProgress;
      case 'daily_challenge':
        const challengeData = data as DailyChallengeData;
        return challengeData.progress / challengeData.target;
      case 'streak':
        const streakData = data as StreakData;
        return Math.min(streakData.count / 7, 1); // Weekly streak
      default:
        return 0;
    }
  };

  const isCompleted = (): boolean => {
    switch (type) {
      case 'achievement':
        return (data as AchievementData).unlocked;
      case 'daily_challenge':
        return (data as DailyChallengeData).completed;
      case 'streak':
        return (data as StreakData).count >= 7;
      default:
        return false;
    }
  };

  const getGradientColors = (): string[] => {
    switch (type) {
      case 'level':
        return [Colors.gamification.level, '#FFE082'];
      case 'streak':
        return [Colors.gamification.streak, '#FF8A65'];
      case 'achievement':
        return isCompleted() 
          ? [Colors.gamification.achievement, '#CE93D8']
          : [Colors.text.light, Colors.border.medium];
      case 'points':
        return [Colors.gamification.reward, '#64B5F6'];
      case 'daily_challenge':
        return isCompleted()
          ? [Colors.gamification.xp, '#81C784']
          : [Colors.psychology.energy, '#FFB74D'];
      default:
        return [Colors.primary.start, Colors.primary.end];
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const renderLevelProgress = (levelData: LevelData) => (
    <View style={styles.levelContainer}>
      <View style={styles.levelHeader}>
        <View style={styles.levelBadge}>
          <LinearGradient
            colors={getGradientColors()}
            style={styles.levelBadgeGradient}
          >
            <Text style={styles.levelNumber}>{levelData.currentLevel}</Text>
          </LinearGradient>
        </View>
        <View style={styles.levelInfo}>
          <Text style={styles.levelName}>{levelData.levelName}</Text>
          <Text style={styles.levelNext}>Next: {levelData.nextLevelName}</Text>
        </View>
        <View style={styles.levelXP}>
          <Text style={styles.xpText}>{levelData.currentXP}</Text>
          <Text style={styles.xpLabel}>XP</Text>
        </View>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressTrack}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
                backgroundColor: Colors.gamification.xp,
              },
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {levelData.currentXP}/{levelData.xpToNextLevel} XP
        </Text>
      </View>
      
      <View style={styles.perksContainer}>
        {levelData.perks.slice(0, 2).map((perk, index) => (
          <View key={index} style={styles.perk}>
            <FontAwesome name="gift" size={10} color={Colors.gamification.reward} />
            <Text style={styles.perkText}>{perk}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderStreakProgress = (streakData: StreakData) => (
    <View style={styles.streakContainer}>
      <View style={styles.streakHeader}>
        <FontAwesome 
          name="fire" 
          size={20} 
          color={streakData.isActive ? Colors.gamification.streak : Colors.text.light} 
        />
        <View style={styles.streakInfo}>
          <Text style={styles.streakCount}>{streakData.count} Day Streak</Text>
          <Text style={styles.streakType}>{streakData.type.replace('_', ' ')}</Text>
        </View>
        <Text style={styles.streakMax}>Best: {streakData.maxStreak}</Text>
      </View>
      
      <View style={styles.streakDots}>
        {Array.from({ length: 7 }, (_, index) => (
          <View
            key={index}
            style={[
              styles.streakDot,
              {
                backgroundColor: index < streakData.count 
                  ? Colors.gamification.streak 
                  : Colors.border.light,
              },
            ]}
          />
        ))}
      </View>
      
      <Text style={styles.streakReward}>
        Next reward: {streakData.nextReward} points
      </Text>
    </View>
  );

  const renderAchievementProgress = (achievementData: AchievementData) => (
    <Animated.View 
      style={[
        styles.achievementContainer,
        { transform: [{ scale: pulseAnim }] },
        isCompleted() && {
          shadowColor: Colors.gamification.achievement,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: glowAnim,
          shadowRadius: 10,
        },
      ]}
    >
      <View style={styles.achievementHeader}>
        <View style={[styles.achievementIcon, isCompleted() && styles.achievementIconCompleted]}>
          <Text style={styles.achievementEmoji}>{achievementData.icon}</Text>
          {isCompleted() && (
            <View style={styles.checkmark}>
              <FontAwesome name="check" size={8} color="#FFFFFF" />
            </View>
          )}
        </View>
        <View style={styles.achievementInfo}>
          <Text style={[styles.achievementTitle, isCompleted() && styles.completedText]}>
            {achievementData.title}
          </Text>
          <Text style={styles.achievementDesc}>{achievementData.description}</Text>
        </View>
        <View style={styles.achievementPoints}>
          <FontAwesome name="star" size={12} color={Colors.gamification.achievement} />
          <Text style={styles.pointsText}>{achievementData.points}</Text>
        </View>
      </View>
      
      {!isCompleted() && (
        <View style={styles.progressContainer}>
          <View style={styles.progressTrack}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                  backgroundColor: Colors.gamification.achievement,
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {achievementData.progress}/{achievementData.maxProgress}
          </Text>
        </View>
      )}
    </Animated.View>
  );

  const renderPointsProgress = (pointsData: PointsData) => (
    <View style={styles.pointsContainer}>
      <LinearGradient
        colors={getGradientColors()}
        style={styles.pointsGradient}
      >
        <View style={styles.pointsHeader}>
          <FontAwesome name="diamond" size={16} color="#FFFFFF" />
          <Text style={styles.pointsTitle}>Loyalty Points</Text>
        </View>
        
        <View style={styles.pointsRow}>
          <View style={styles.pointsItem}>
            <Text style={styles.pointsValue}>{pointsData.current.toLocaleString()}</Text>
            <Text style={styles.pointsLabel}>Total</Text>
          </View>
          <View style={styles.pointsItem}>
            <Text style={styles.pointsValue}>+{pointsData.earned}</Text>
            <Text style={styles.pointsLabel}>Earned Today</Text>
          </View>
          <View style={styles.pointsItem}>
            <Text style={styles.pointsValue}>{pointsData.redeemable}</Text>
            <Text style={styles.pointsLabel}>Redeemable</Text>
          </View>
        </View>
        
        <Text style={styles.nextReward}>
          {pointsData.nextReward - pointsData.current} points to next reward
        </Text>
      </LinearGradient>
    </View>
  );

  const renderDailyChallengeProgress = (challengeData: DailyChallengeData) => (
    <View style={[styles.challengeContainer, isCompleted() && styles.challengeCompleted]}>
      <View style={styles.challengeHeader}>
        <View style={styles.challengeIcon}>
          <FontAwesome 
            name={isCompleted() ? "check-circle" : "target"} 
            size={16} 
            color={isCompleted() ? Colors.gamification.xp : Colors.psychology.energy} 
          />
        </View>
        <View style={styles.challengeInfo}>
          <Text style={styles.challengeTitle}>{challengeData.title}</Text>
          <Text style={styles.challengeDesc}>{challengeData.description}</Text>
        </View>
        <View style={styles.challengeReward}>
          <FontAwesome name="gift" size={12} color={Colors.gamification.reward} />
          <Text style={styles.rewardText}>{challengeData.reward}</Text>
        </View>
      </View>
      
      {!isCompleted() && (
        <>
          <View style={styles.progressContainer}>
            <View style={styles.progressTrack}>
              <Animated.View
                style={[
                  styles.progressFill,
                  {
                    width: progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                    backgroundColor: Colors.psychology.energy,
                  },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {challengeData.progress}/{challengeData.target}
            </Text>
          </View>
          
          <View style={styles.challengeFooter}>
            <FontAwesome name="clock-o" size={10} color={Colors.text.secondary} />
            <Text style={styles.timeLeft}>
              {formatTime(challengeData.timeLeft)} left
            </Text>
          </View>
        </>
      )}
      
      {isCompleted() && (
        <Animatable.View animation="bounceIn" style={styles.completedBadge}>
          <Text style={styles.completedText}>Challenge Completed! 🎉</Text>
        </Animatable.View>
      )}
    </View>
  );

  const renderContent = () => {
    switch (type) {
      case 'level':
        return renderLevelProgress(data as LevelData);
      case 'streak':
        return renderStreakProgress(data as StreakData);
      case 'achievement':
        return renderAchievementProgress(data as AchievementData);
      case 'points':
        return renderPointsProgress(data as PointsData);
      case 'daily_challenge':
        return renderDailyChallengeProgress(data as DailyChallengeData);
      default:
        return null;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background.card,
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Level Progress Styles
  levelContainer: {},
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  levelBadge: {
    marginRight: 12,
  },
  levelBadgeGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  levelNumber: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  levelInfo: {
    flex: 1,
  },
  levelName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  levelNext: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  levelXP: {
    alignItems: 'flex-end',
  },
  xpText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.gamification.xp,
  },
  xpLabel: {
    fontSize: 10,
    color: Colors.text.secondary,
  },
  perksContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  perk: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  perkText: {
    fontSize: 10,
    color: Colors.gamification.reward,
    marginLeft: 4,
  },

  // Streak Progress Styles
  streakContainer: {},
  streakHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  streakInfo: {
    flex: 1,
    marginLeft: 8,
  },
  streakCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  streakType: {
    fontSize: 12,
    color: Colors.text.secondary,
    textTransform: 'capitalize',
  },
  streakMax: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  streakDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 8,
  },
  streakDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
  streakReward: {
    fontSize: 12,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  // Achievement Progress Styles
  achievementContainer: {},
  achievementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  achievementIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    position: 'relative',
  },
  achievementIconCompleted: {
    backgroundColor: Colors.gamification.achievement,
  },
  achievementEmoji: {
    fontSize: 18,
  },
  checkmark: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: Colors.gamification.xp,
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  achievementDesc: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  achievementPoints: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pointsText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.gamification.achievement,
    marginLeft: 2,
  },
  completedText: {
    color: Colors.gamification.achievement,
  },

  // Points Progress Styles
  pointsContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  pointsGradient: {
    padding: 16,
  },
  pointsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  pointsTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  pointsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  pointsItem: {
    alignItems: 'center',
  },
  pointsValue: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  pointsLabel: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 10,
  },
  nextReward: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 12,
    textAlign: 'center',
  },

  // Daily Challenge Progress Styles
  challengeContainer: {
    borderLeftWidth: 4,
    borderLeftColor: Colors.psychology.energy,
    paddingLeft: 12,
  },
  challengeCompleted: {
    borderLeftColor: Colors.gamification.xp,
    backgroundColor: 'rgba(76, 175, 80, 0.05)',
  },
  challengeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  challengeIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  challengeInfo: {
    flex: 1,
  },
  challengeTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  challengeDesc: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  challengeReward: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rewardText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.gamification.reward,
    marginLeft: 2,
  },
  challengeFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  timeLeft: {
    fontSize: 10,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  completedBadge: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
  },

  // Common Progress Styles
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  progressTrack: {
    flex: 1,
    height: 6,
    backgroundColor: Colors.border.light,
    borderRadius: 3,
    marginRight: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 10,
    color: Colors.text.secondary,
    minWidth: 40,
    textAlign: 'right',
  },
});

export default GamificationProgress;