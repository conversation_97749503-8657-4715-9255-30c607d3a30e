import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import * as Animatable from 'react-native-animatable';

interface SocialProofProps {
  type: 'activity' | 'reviews' | 'trending' | 'friends' | 'verified';
  data?: any;
  style?: any;
}

interface ActivityData {
  count: number;
  timeframe: string;
  action: string;
}

interface ReviewData {
  rating: number;
  count: number;
  recentReview?: {
    user: string;
    text: string;
    rating: number;
    avatar?: string;
  };
}

interface TrendingData {
  rank: number;
  category: string;
  growth: number;
}

interface FriendsData {
  count: number;
  names: string[];
  avatars: string[];
}

interface VerifiedData {
  badges: string[];
  certifications: string[];
}

const SocialProof: React.FC<SocialProofProps> = ({ type, data, style }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <FontAwesome key={i} name="star" size={12} color={Colors.social.reviews} />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <FontAwesome key="half" name="star-half-o" size={12} color={Colors.social.reviews} />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <FontAwesome key={`empty-${i}`} name="star-o" size={12} color={Colors.text.light} />
      );
    }

    return stars;
  };

  const renderActivityProof = (activityData: ActivityData) => (
    <Animatable.View animation="slideInRight" duration={800} style={[styles.container, style]}>
      <LinearGradient
        colors={[Colors.social.trending, Colors.social.popular]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.activityContent}>
          <View style={styles.activityIcon}>
            <FontAwesome name="fire" size={16} color="#FFFFFF" />
          </View>
          <View style={styles.activityText}>
            <Text style={styles.activityCount}>{activityData.count}+</Text>
            <Text style={styles.activityDescription}>
              {activityData.action} in {activityData.timeframe}
            </Text>
          </View>
        </View>
      </LinearGradient>
    </Animatable.View>
  );

  const renderReviewProof = (reviewData: ReviewData) => (
    <Animatable.View animation="fadeInUp" duration={600} style={[styles.container, style]}>
      <View style={styles.reviewContainer}>
        <View style={styles.reviewHeader}>
          <View style={styles.starsContainer}>
            {renderStars(reviewData.rating)}
          </View>
          <Text style={styles.reviewRating}>{reviewData.rating}</Text>
          <Text style={styles.reviewCount}>({reviewData.count} reviews)</Text>
        </View>
        {reviewData.recentReview && (
          <View style={styles.recentReview}>
            <View style={styles.reviewerInfo}>
              {reviewData.recentReview.avatar ? (
                <Image
                  source={{ uri: reviewData.recentReview.avatar }}
                  style={styles.reviewerAvatar}
                />
              ) : (
                <View style={styles.reviewerAvatarPlaceholder}>
                  <FontAwesome name="user" size={12} color={Colors.text.light} />
                </View>
              )}
              <Text style={styles.reviewerName}>{reviewData.recentReview.user}</Text>
            </View>
            <Text style={styles.reviewText} numberOfLines={2}>
              "{reviewData.recentReview.text}"
            </Text>
          </View>
        )}
      </View>
    </Animatable.View>
  );

  const renderTrendingProof = (trendingData: TrendingData) => (
    <Animatable.View animation="pulse" duration={1000} style={[styles.container, style]}>
      <LinearGradient
        colors={[Colors.offers.popular[0], Colors.offers.popular[1]]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.trendingContent}>
          <FontAwesome name="trending-up" size={14} color="#FFFFFF" style={styles.trendingIcon} />
          <Text style={styles.trendingText}>
            #{trendingData.rank} in {trendingData.category}
          </Text>
          <View style={styles.growthBadge}>
            <FontAwesome name="arrow-up" size={8} color="#FFFFFF" />
            <Text style={styles.growthText}>+{trendingData.growth}%</Text>
          </View>
        </View>
      </LinearGradient>
    </Animatable.View>
  );

  const renderFriendsProof = (friendsData: FriendsData) => (
    <Animatable.View animation="slideInLeft" duration={700} style={[styles.container, style]}>
      <View style={styles.friendsContainer}>
        <View style={styles.friendsAvatars}>
          {friendsData.avatars.slice(0, 3).map((avatar, index) => (
            <Image
              key={index}
              source={{ uri: avatar }}
              style={[styles.friendAvatar, { marginLeft: index > 0 ? -8 : 0 }]}
            />
          ))}
          {friendsData.count > 3 && (
            <View style={[styles.friendAvatar, styles.moreIndicator, { marginLeft: -8 }]}>
              <Text style={styles.moreText}>+{friendsData.count - 3}</Text>
            </View>
          )}
        </View>
        <View style={styles.friendsText}>
          <Text style={styles.friendsNames}>
            {friendsData.names.slice(0, 2).join(', ')}
            {friendsData.count > 2 && ` and ${friendsData.count - 2} others`}
          </Text>
          <Text style={styles.friendsAction}>also bought this</Text>
        </View>
        <FontAwesome name="users" size={12} color={Colors.social.friends} />
      </View>
    </Animatable.View>
  );

  const renderVerifiedProof = (verifiedData: VerifiedData) => (
    <Animatable.View animation="bounceIn" duration={800} style={[styles.container, style]}>
      <View style={styles.verifiedContainer}>
        <View style={styles.verifiedBadges}>
          {verifiedData.badges.map((badge, index) => (
            <View key={index} style={styles.badge}>
              <FontAwesome name="check-circle" size={10} color={Colors.social.verified} />
              <Text style={styles.badgeText}>{badge}</Text>
            </View>
          ))}
        </View>
        {verifiedData.certifications.length > 0 && (
          <View style={styles.certifications}>
            <FontAwesome name="certificate" size={12} color={Colors.psychology.luxury} />
            <Text style={styles.certificationText}>
              {verifiedData.certifications.join(' • ')}
            </Text>
          </View>
        )}
      </View>
    </Animatable.View>
  );

  if (!isVisible || !data) return null;

  switch (type) {
    case 'activity':
      return renderActivityProof(data as ActivityData);
    case 'reviews':
      return renderReviewProof(data as ReviewData);
    case 'trending':
      return renderTrendingProof(data as TrendingData);
    case 'friends':
      return renderFriendsProof(data as FriendsData);
    case 'verified':
      return renderVerifiedProof(data as VerifiedData);
    default:
      return null;
  }
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    borderRadius: 8,
    overflow: 'hidden',
  },
  gradient: {
    padding: 8,
    borderRadius: 8,
  },
  
  // Activity styles
  activityContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  activityText: {
    flex: 1,
  },
  activityCount: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  activityDescription: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 11,
  },
  
  // Review styles
  reviewContainer: {
    backgroundColor: Colors.background.card,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 6,
  },
  reviewRating: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginRight: 4,
  },
  reviewCount: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  recentReview: {
    marginTop: 8,
  },
  reviewerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  reviewerAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 6,
  },
  reviewerAvatarPlaceholder: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  reviewerName: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  reviewText: {
    fontSize: 11,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  
  // Trending styles
  trendingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  trendingIcon: {
    marginRight: 6,
  },
  trendingText: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  growthBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  growthText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    marginLeft: 2,
  },
  
  // Friends styles
  friendsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.card,
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  friendsAvatars: {
    flexDirection: 'row',
    marginRight: 8,
  },
  friendAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.background.card,
  },
  moreIndicator: {
    backgroundColor: Colors.text.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: 'bold',
  },
  friendsText: {
    flex: 1,
  },
  friendsNames: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  friendsAction: {
    fontSize: 11,
    color: Colors.text.secondary,
  },
  
  // Verified styles
  verifiedContainer: {
    backgroundColor: Colors.background.card,
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.social.verified,
  },
  verifiedBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 6,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 4,
    marginBottom: 2,
  },
  badgeText: {
    fontSize: 10,
    color: Colors.social.verified,
    fontWeight: '600',
    marginLeft: 2,
  },
  certifications: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  certificationText: {
    fontSize: 10,
    color: Colors.psychology.luxury,
    marginLeft: 4,
    fontWeight: '500',
  },
});

export default SocialProof;