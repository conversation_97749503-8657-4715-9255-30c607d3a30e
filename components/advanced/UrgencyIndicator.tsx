import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import Colors from '../../constants/Colors';

interface UrgencyIndicatorProps {
  type: 'stock' | 'time' | 'price' | 'demand' | 'trending';
  message: string;
  countdown?: number;
  intensity?: 'low' | 'medium' | 'high';
  stockCount?: number;
  priceChange?: number;
  trendingCount?: number;
  style?: any;
}

const UrgencyIndicator: React.FC<UrgencyIndicatorProps> = ({
  type,
  message,
  countdown,
  intensity = 'medium',
  stockCount,
  priceChange,
  trendingCount,
  style,
}) => {
  const [timeLeft, setTimeLeft] = useState(countdown || 0);
  const [pulseAnim] = useState(new Animated.Value(1));
  const [shakeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (countdown && countdown > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [countdown]);

  useEffect(() => {
    // Pulse animation for high intensity
    if (intensity === 'high') {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }

    // Shake animation for stock alerts
    if (type === 'stock' && stockCount && stockCount <= 3) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(shakeAnim, {
            toValue: 1,
            duration: 50,
            useNativeDriver: true,
          }),
          Animated.timing(shakeAnim, {
            toValue: -1,
            duration: 50,
            useNativeDriver: true,
          }),
          Animated.timing(shakeAnim, {
            toValue: 0,
            duration: 50,
            useNativeDriver: true,
          }),
        ]),
        { iterations: 3 }
      ).start();
    }
  }, [intensity, type, stockCount]);

  const getColors = () => {
    switch (type) {
      case 'stock':
        return intensity === 'high' 
          ? [Colors.urgency.high, '#FF4569']
          : [Colors.urgency.medium, Colors.urgency.high];
      case 'time':
        return [Colors.urgency.countdown, '#FF6B35'];
      case 'price':
        return [Colors.psychology.energy, Colors.offers.discount[1]];
      case 'demand':
        return [Colors.social.trending, Colors.social.popular];
      case 'trending':
        return [Colors.offers.popular[0], Colors.offers.popular[1]];
      default:
        return [Colors.urgency.medium, Colors.urgency.high];
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'stock':
        return 'exclamation-triangle';
      case 'time':
        return 'clock-o';
      case 'price':
        return 'arrow-down';
      case 'demand':
        return 'fire';
      case 'trending':
        return 'line-chart';
      default:
        return 'bell';
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const renderContent = () => {
    switch (type) {
      case 'stock':
        return (
          <View style={styles.contentRow}>
            <FontAwesome name={getIcon()} size={12} color="#FFFFFF" style={styles.icon} />
            <Text style={styles.text}>
              {stockCount && stockCount <= 5 
                ? `Only ${stockCount} left!` 
                : message}
            </Text>
          </View>
        );
      
      case 'time':
        return (
          <View style={styles.contentRow}>
            <FontAwesome name={getIcon()} size={12} color="#FFFFFF" style={styles.icon} />
            <Text style={styles.text}>
              {timeLeft > 0 ? `${formatTime(timeLeft)} left!` : 'Offer Expired!'}
            </Text>
          </View>
        );
      
      case 'price':
        return (
          <View style={styles.contentRow}>
            <FontAwesome name={getIcon()} size={12} color="#FFFFFF" style={styles.icon} />
            <Text style={styles.text}>
              {priceChange ? `₹${priceChange} OFF!` : message}
            </Text>
          </View>
        );
      
      case 'demand':
        return (
          <View style={styles.contentRow}>
            <FontAwesome name={getIcon()} size={12} color="#FFFFFF" style={styles.icon} />
            <Text style={styles.text}>🔥 High Demand!</Text>
          </View>
        );
      
      case 'trending':
        return (
          <View style={styles.contentRow}>
            <FontAwesome name="line-chart" size={12} color="#FFFFFF" style={styles.icon} />
            <Text style={styles.text}>
              {trendingCount ? `${trendingCount}+ bought today` : 'Trending Now!'}
            </Text>
          </View>
        );
      
      default:
        return (
          <View style={styles.contentRow}>
            <FontAwesome name={getIcon()} size={12} color="#FFFFFF" style={styles.icon} />
            <Text style={styles.text}>{message}</Text>
          </View>
        );
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        style,
        {
          transform: [
            { scale: pulseAnim },
            { translateX: shakeAnim.interpolate({
              inputRange: [-1, 0, 1],
              outputRange: [-2, 0, 2],
            }) }
          ],
        },
      ]}
    >
      <LinearGradient
        colors={getColors()}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        {renderContent()}
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 2,
  },
  gradient: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: 4,
  },
  text: {
    color: '#FFFFFF',
    fontSize: 11,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default UrgencyIndicator;