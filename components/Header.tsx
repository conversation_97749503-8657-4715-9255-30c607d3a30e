import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { memo, useCallback, useEffect, useRef } from 'react';
import { Animated, Easing, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Colors from '../constants/Colors';
import { createTextShadow } from '../utils/shadows';

interface HeaderProps {
  location: string;
}

// Check if running on web
const IS_WEB = Platform.OS === 'web';

export const Header: React.FC<HeaderProps> = memo(({ location }) => {
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const locationAnim = useRef(new Animated.Value(0)).current;
  const subtitleAnim = useRef(new Animated.Value(0)).current;
  const scaleLoc = useRef(new Animated.Value(0.9)).current;
  
  // Animate location bounce - memoized callback for better performance
  const pulseLocation = useCallback(() => {
    Animated.sequence([
      Animated.timing(scaleLoc, {
        toValue: 1.05,
        duration: 200,
        useNativeDriver: !IS_WEB,
        easing: Easing.out(Easing.ease),
      }),
      Animated.timing(scaleLoc, {
        toValue: 1,
        duration: 100,
        useNativeDriver: !IS_WEB,
        easing: Easing.inOut(Easing.ease),
      }),
    ]).start();
  }, [scaleLoc]);

  useEffect(() => {
    // Staggered entrance animations
    Animated.stagger(150, [
      Animated.timing(locationAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: !IS_WEB,
        easing: Easing.out(Easing.back(1.5)),
      }),
      Animated.timing(titleOpacity, {
        toValue: 1,
        duration: 400,
        useNativeDriver: !IS_WEB,
      }),
      Animated.timing(subtitleAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: !IS_WEB,
      }),
    ]).start();
    
    // Pulse animation for location after initial animation
    const pulseTimeout = setTimeout(pulseLocation, 1000);
    
    // Cleanup timeout
    return () => clearTimeout(pulseTimeout);
  }, []);

  return (
    <LinearGradient
      colors={[Colors.primary.start, Colors.primary.end]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.container}
    >
      <Animated.View 
        style={[
          styles.locationWrapper,
          {
            opacity: locationAnim,
            transform: [
              { translateY: locationAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-10, 0]
              })},
              { scale: scaleLoc }
            ]
          }
        ]}
      >
        <TouchableOpacity style={styles.locationContainer} onPress={pulseLocation}>
          <FontAwesome name="map-marker" size={14} color="#FFFFFF" style={styles.locationIcon} />
          <Text style={styles.locationText}>{location}</Text>
          <FontAwesome name="chevron-down" size={12} color="#FFFFFF" />
        </TouchableOpacity>
      </Animated.View>
      
      <Animated.Text 
        style={[
          styles.title,
          {
            opacity: titleOpacity,
            transform: [
              { translateY: titleOpacity.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })}
            ]
          }
        ]}
      >
        Delivery in 10 minutes
      </Animated.Text>
      
      <Animated.Text 
        style={[
          styles.subtitle,
          {
            opacity: subtitleAnim,
            transform: [
              { translateY: subtitleAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [10, 0]
              })}
            ]
          }
        ]}
      >
        Lightning fast groceries & essentials
      </Animated.Text>

      {/* Decorative elements */}
      <View style={styles.decorativeCircle} />
      <View style={styles.decorativeCircleSmall} />
    </LinearGradient>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 24,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  locationWrapper: {
    alignSelf: 'flex-start',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  locationIcon: {
    marginRight: 5,
  },
  locationText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 5,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    ...createTextShadow({
      textShadowColor: 'rgba(0, 0, 0, 0.1)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 5,
    }),
  },
  subtitle: {
    color: '#FFFFFF',
    fontSize: 16,
    opacity: 0.9,
  },
  decorativeCircle: {
    position: 'absolute',
    top: -100,
    right: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircleSmall: {
    position: 'absolute',
    bottom: -30,
    left: -30,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
});

export default Header; 