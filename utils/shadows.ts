import { Platform, ViewStyle, TextStyle } from 'react-native';

export interface ShadowConfig {
  elevation?: number;
  shadowColor?: string;
  shadowOffset?: { width: number; height: number };
  shadowOpacity?: number;
  shadowRadius?: number;
}

export interface TextShadowConfig {
  textShadowColor?: string;
  textShadowOffset?: { width: number; height: number };
  textShadowRadius?: number;
}

/**
 * Creates cross-platform shadow styles that work on iOS, Android, and Web
 * @param config Shadow configuration
 * @returns Platform-specific shadow styles
 */
export const createShadow = (config: ShadowConfig): ViewStyle => {
  const {
    elevation = 4,
    shadowColor = '#000',
    shadowOffset = { width: 0, height: 2 },
    shadowOpacity = 0.1,
    shadowRadius = 4,
  } = config;

  if (Platform.OS === 'web') {
    // Use CSS box-shadow for web
    const offsetX = shadowOffset.width;
    const offsetY = shadowOffset.height;
    const blur = shadowRadius;
    const spread = 0;
    const alpha = shadowOpacity;
    
    // Convert hex color to rgba if needed
    let shadowColorRgba = shadowColor;
    if (shadowColor.startsWith('#')) {
      const hex = shadowColor.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      shadowColorRgba = `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    return {
      boxShadow: `${offsetX}px ${offsetY}px ${blur}px ${spread}px ${shadowColorRgba}`,
    } as ViewStyle;
  }

  if (Platform.OS === 'android') {
    return {
      elevation,
    };
  }

  // iOS
  return {
    shadowColor,
    shadowOffset,
    shadowOpacity,
    shadowRadius,
  };
};

/**
 * Creates cross-platform text shadow styles
 * @param config Text shadow configuration
 * @returns Platform-specific text shadow styles
 */
export const createTextShadow = (config: TextShadowConfig): TextStyle => {
  const {
    textShadowColor = 'rgba(0, 0, 0, 0.1)',
    textShadowOffset = { width: 0, height: 1 },
    textShadowRadius = 3,
  } = config;

  if (Platform.OS === 'web') {
    // Use CSS text-shadow for web
    const offsetX = textShadowOffset.width;
    const offsetY = textShadowOffset.height;
    const blur = textShadowRadius;
    
    return {
      textShadow: `${offsetX}px ${offsetY}px ${blur}px ${textShadowColor}`,
    } as TextStyle;
  }

  // iOS and Android support these properties natively
  return {
    textShadowColor,
    textShadowOffset,
    textShadowRadius,
  };
};

/**
 * Predefined shadow presets for common use cases
 */
export const shadowPresets = {
  none: createShadow({ elevation: 0, shadowOpacity: 0 }),
  
  small: createShadow({
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  }),
  
  medium: createShadow({
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  }),
  
  large: createShadow({
    elevation: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  }),
  
  floating: createShadow({
    elevation: 12,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
  }),
};

/**
 * Predefined text shadow presets
 */
export const textShadowPresets = {
  none: createTextShadow({ textShadowColor: 'transparent' }),
  
  subtle: createTextShadow({
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  }),
  
  medium: createTextShadow({
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  }),
  
  strong: createTextShadow({
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  }),
};
