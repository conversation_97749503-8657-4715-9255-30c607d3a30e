/**
 * Psychologically optimized colors for maximum user engagement and conversion
 * Based on color psychology principles used by top e-commerce apps
 */

const tintColorLight = '#FF6B35'; // Energetic orange for action
const tintColorDark = '#FF8A65';

export const Colors = {
  // Primary brand colors - Orange/Red for urgency and appetite stimulation
  primary: {
    start: '#FF6B35', // Vibrant orange - creates urgency, stimulates appetite
    end: '#FF5722',   // Deep orange-red - triggers action
    light: '#FFB74D', // Warm orange - friendly and approachable
    dark: '#E65100',  // Dark orange - premium feel
  },
  
  // Secondary colors - Green for trust, growth, and eco-friendliness
  secondary: {
    start: '#4CAF50', // Fresh green - growth, natural, trustworthy
    end: '#388E3C',   // Deep green - stability, prosperity
    light: '#81C784', // Light green - calm, peaceful
    dark: '#2E7D32',  // Dark green - luxury, premium
  },
  
  // Accent colors for highlights and CTAs
  accent: {
    gold: '#FFD700',    // Gold - premium, exclusive
    coral: '#FF7043',   // Coral - warm, friendly
    teal: '#26A69A',    // Teal - balance, sophistication
    purple: '#9C27B0',  // Purple - creativity, luxury
  },
  
  // Background colors
  background: {
    primary: '#FFFFFF',     // Pure white - clean, trustworthy
    secondary: '#FAFAFA',   // Off-white - soft, comfortable
    tertiary: '#F5F5F5',    // Light gray - neutral, modern
    card: '#FFFFFF',        // White cards - prominence
    overlay: 'rgba(0,0,0,0.7)', // Dark overlay - focus
  },
  
  // Text colors with psychological impact
  text: {
    primary: '#212121',     // Dark gray - easy to read, professional
    secondary: '#757575',   // Medium gray - supportive information
    light: '#9E9E9E',      // Light gray - subtle details
    white: '#FFFFFF',      // White - contrast on dark backgrounds
    link: '#1976D2',       // Blue - trust, reliability
    error: '#F44336',      // Red - urgency, warning
    success: '#4CAF50',    // Green - positive, success
    warning: '#FF9800',    // Orange - attention, caution
  },
  
  // Gamification colors
  gamification: {
    xp: '#4CAF50',         // Green for experience points
    level: '#FFD700',      // Gold for level progression
    achievement: '#9C27B0', // Purple for achievements
    streak: '#FF5722',     // Orange-red for streaks
    reward: '#2196F3',     // Blue for rewards
    badge: '#FF9800',      // Amber for badges
  },
  
  // Category colors with appetite psychology
  categories: {
    vegetables: ['#4CAF50', '#66BB6A'], // Fresh greens - natural, healthy
    fruits: ['#FF5722', '#FF7043'],     // Warm reds/oranges - appetite stimulation
    dairy: ['#2196F3', '#42A5F5'],      // Cool blues - fresh, clean
    meat: ['#795548', '#8D6E63'],       // Browns - natural, hearty
    bakery: ['#FF9800', '#FFB74D'],     // Warm oranges - comfort, freshness
    snacks: ['#E91E63', '#F06292'],     // Pinks/magentas - fun, indulgent
    beverages: ['#00BCD4', '#26C6DA'],  // Cyans - refreshing, cool
    personalCare: ['#9C27B0', '#BA68C8'], // Purples - luxury, self-care
    household: ['#607D8B', '#78909C'],  // Blue-grays - clean, practical
    babyCare: ['#FFC107', '#FFCA28'],   // Soft yellows - gentle, caring
    health: ['#4CAF50', '#66BB6A'],     // Greens - wellness, vitality
    frozen: ['#03A9F4', '#29B6F6'],     // Light blues - cold, preserved
    pantry: ['#795548', '#A1887F'],     // Earth tones - staples, reliable
    organic: ['#8BC34A', '#9CCC65'],    // Lime greens - natural, premium
  },
  
  // Offer and promotion colors (high psychological impact)
  offers: {
    flash: ['#FF1744', '#FF5252'],      // Bright reds - extreme urgency
    limited: ['#FF6F00', '#FF8F00'],    // Amber - limited time urgency
    discount: ['#4CAF50', '#66BB6A'],   // Green - savings, positive
    premium: ['#9C27B0', '#BA68C8'],    // Purple - exclusive, luxury
    new: ['#2196F3', '#42A5F5'],        // Blue - trust, innovation
    popular: ['#FF9800', '#FFB74D'],    // Orange - trending, social proof
    seasonal: ['#E91E63', '#F06292'],   // Pink - special, festive
  },
  
  // Status colors for orders and delivery
  status: {
    pending: '#FF9800',     // Orange - waiting, in progress
    processing: '#2196F3',  // Blue - active, working
    confirmed: '#4CAF50',   // Green - positive, approved
    shipped: '#9C27B0',     // Purple - premium service
    delivered: '#4CAF50',   // Green - success, completion
    cancelled: '#F44336',   // Red - stopped, negative
    refunded: '#607D8B',    // Gray - neutral, processed
  },
  
  // Urgency and scarcity colors (FOMO triggers)
  urgency: {
    high: '#FF1744',        // Bright red - immediate action needed
    medium: '#FF6F00',      // Orange - some urgency
    low: '#FF9800',         // Amber - mild urgency
    countdown: '#FF3D00',   // Deep orange-red - time pressure
    stock: '#F44336',       // Red - scarcity alert
    price: '#4CAF50',       // Green - price drop alert
  },
  
  // Social proof colors
  social: {
    trending: '#FF5722',    // Orange-red - hot, popular
    popular: '#FF9800',     // Orange - well-liked
    friends: '#2196F3',     // Blue - social connection
    reviews: '#FFD700',     // Gold - ratings, quality
    verified: '#4CAF50',    // Green - authentic, trusted
  },
  
  // Border and divider colors
  border: {
    light: '#E0E0E0',       // Light gray - subtle separation
    medium: '#BDBDBD',      // Medium gray - clear division
    dark: '#757575',        // Dark gray - strong separation
    accent: '#FF6B35',      // Brand color - highlighted borders
  },
  
  // Shadow colors for depth psychology
  shadow: {
    light: 'rgba(0,0,0,0.08)',   // Subtle depth
    medium: 'rgba(0,0,0,0.16)',  // Clear elevation
    heavy: 'rgba(0,0,0,0.24)',   // Strong depth
    colored: 'rgba(255,107,53,0.3)', // Brand-colored shadow
  },
  
  // Theme variants
  light: {
    text: '#212121',
    background: '#FFFFFF',
    surface: '#FAFAFA',
    tint: tintColorLight,
    icon: '#757575',
    tabIconDefault: '#9E9E9E',
    tabIconSelected: tintColorLight,
    subtleText: '#757575',
    border: '#E0E0E0',
    card: '#FFFFFF',
    notification: '#FF6B35',
  },
  
  dark: {
    text: '#FFFFFF',
    background: '#121212',
    surface: '#1E1E1E',
    tint: tintColorDark,
    icon: '#BBBBBB',
    tabIconDefault: '#777777',
    tabIconSelected: tintColorDark,
    subtleText: '#AAAAAA',
    border: '#333333',
    card: '#1E1E1E',
    notification: '#FF8A65',
  },
  
  // Gradient combinations for visual appeal
  gradients: {
    primary: ['#FF6B35', '#FF5722'],
    secondary: ['#4CAF50', '#388E3C'],
    success: ['#4CAF50', '#66BB6A'],
    warning: ['#FF9800', '#FFB74D'],
    error: ['#F44336', '#EF5350'],
    info: ['#2196F3', '#42A5F5'],
    premium: ['#9C27B0', '#BA68C8'],
    sunset: ['#FF6B35', '#FF9800', '#FFD700'],
    ocean: ['#2196F3', '#00BCD4', '#4CAF50'],
    fire: ['#FF1744', '#FF5722', '#FF9800'],
  },
  
  // Psychological trigger colors
  psychology: {
    appetite: '#FF5722',     // Orange-red stimulates hunger
    trust: '#2196F3',        // Blue builds trust
    luxury: '#9C27B0',       // Purple suggests premium
    nature: '#4CAF50',       // Green implies natural/organic
    energy: '#FF6B35',       // Orange creates energy/urgency
    calm: '#00BCD4',         // Cyan promotes calmness
    excitement: '#E91E63',   // Pink/magenta creates excitement
    stability: '#607D8B',    // Blue-gray suggests reliability
  },
};

export default Colors;