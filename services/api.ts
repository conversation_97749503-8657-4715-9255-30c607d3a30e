import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ApiService {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = __DEV__ 
      ? 'http://localhost:3000/api' 
      : 'https://your-secure-api.com/api';
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      this.authToken = token;
    } catch (error) {
      console.error('Error initializing auth:', error);
    }
  }

  private async checkConnection(): Promise<boolean> {
    const netInfo = await NetInfo.fetch();
    return netInfo.isConnected ?? false;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const isConnected = await this.checkConnection();
      if (!isConnected) {
        throw new Error('No internet connection');
      }

      const url = `${this.baseURL}${endpoint}`;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('API Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Authentication
  async login(phone: string, otp: string): Promise<ApiResponse<{ token: string; user: any }>> {
    // Simulate API call for demo
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (otp === '1234') {
      const mockUser = {
        id: '1',
        name: 'Demo User',
        phone: phone,
        loyaltyPoints: 1250,
        membershipTier: 'Silver' as const,
      };
      
      const token = 'mock_jwt_token_' + Date.now();
      await AsyncStorage.setItem('auth_token', token);
      await AsyncStorage.setItem('user_data', JSON.stringify(mockUser));
      
      this.authToken = token;
      return { success: true, data: { token, user: mockUser } };
    }
    
    return { success: false, error: 'Invalid OTP. Use 1234 for demo.' };
  }

  async logout(): Promise<void> {
    this.authToken = null;
    await AsyncStorage.multiRemove(['auth_token', 'user_data']);
  }

  // Products
  async getCategories(): Promise<ApiResponse<any[]>> {
    // Mock data for demo
    const mockCategories = [
      { id: '1', name: 'Vegetables', emoji: '🥬', colors: ['#4CC417', '#57E32C'] },
      { id: '2', name: 'Fruits', emoji: '🍎', colors: ['#FF5757', '#FF7B54'] },
      { id: '3', name: 'Dairy', emoji: '🥛', colors: ['#3B82F6', '#60A5FA'] },
      { id: '4', name: 'Personal Care', emoji: '🧴', colors: ['#8B5CF6', '#A78BFA'] },
    ];
    
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, data: mockCategories };
  }

  async getProducts(categoryId?: string): Promise<ApiResponse<any[]>> {
    // Mock data for demo
    const mockProducts = [
      { id: '1', name: 'Fresh Onions', price: 29, category: 'vegetables' },
      { id: '2', name: 'Amul Milk', price: 30, category: 'dairy' },
      { id: '3', name: 'Bananas', price: 59, category: 'fruits' },
    ];
    
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, data: mockProducts };
  }

  async searchProducts(query: string): Promise<ApiResponse<any[]>> {
    // Mock search
    const mockResults = [
      { id: '1', name: 'Onions', price: '₹29', category: 'Vegetables' },
      { id: '2', name: 'Milk', price: '₹30', category: 'Dairy' },
    ];
    
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, data: mockResults };
  }

  async getProductDetails(productId: string): Promise<ApiResponse<any>> {
    // Mock product details
    const mockProduct = {
      id: productId,
      name: 'Fresh Onions',
      price: 29,
      description: 'Fresh organic onions',
    };
    
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, data: mockProduct };
  }

  // Cart
  async getCart(): Promise<ApiResponse<any>> {
    const mockCart = { items: [], total: 0 };
    return { success: true, data: mockCart };
  }

  async addToCart(productId: string, quantity: number): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, data: { productId, quantity } };
  }

  async updateCartItem(itemId: string, quantity: number): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, data: { itemId, quantity } };
  }

  async removeFromCart(itemId: string): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, data: { itemId } };
  }

  // Orders
  async getOrders(): Promise<ApiResponse<any[]>> {
    const mockOrders = [
      {
        id: '1',
        orderNumber: 'ZEP-2023-123456',
        status: 'delivered',
        total: 148,
        items: 3,
      },
    ];
    
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, data: mockOrders };
  }

  async getOrderDetails(orderId: string): Promise<ApiResponse<any>> {
    const mockOrder = {
      id: orderId,
      orderNumber: 'ZEP-2023-123456',
      status: 'delivered',
      total: 148,
    };
    
    return { success: true, data: mockOrder };
  }

  async createOrder(orderData: any): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newOrder = {
      id: Date.now().toString(),
      ...orderData,
      status: 'confirmed',
    };
    
    return { success: true, data: newOrder };
  }

  // User Profile
  async getUserProfile(): Promise<ApiResponse<any>> {
    const userData = await AsyncStorage.getItem('user_data');
    if (userData) {
      return { success: true, data: JSON.parse(userData) };
    }
    return { success: false, error: 'No user data found' };
  }

  async updateUserProfile(profileData: any): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    await AsyncStorage.setItem('user_data', JSON.stringify(profileData));
    return { success: true, data: profileData };
  }

  // Addresses
  async getAddresses(): Promise<ApiResponse<any[]>> {
    const mockAddresses = [
      {
        id: '1',
        type: 'home',
        address: 'Sector 62, Noida',
        isDefault: true,
      },
    ];
    
    return { success: true, data: mockAddresses };
  }

  async addAddress(addressData: any): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newAddress = {
      id: Date.now().toString(),
      ...addressData,
    };
    
    return { success: true, data: newAddress };
  }

  // Offers and Rewards
  async getOffers(): Promise<ApiResponse<any[]>> {
    const mockOffers = [
      {
        id: '1',
        title: 'First Order',
        discount: '50% OFF',
        description: 'Get 50% off on your first order',
      },
    ];
    
    return { success: true, data: mockOffers };
  }

  async getUserRewards(): Promise<ApiResponse<any>> {
    const mockRewards = {
      points: 1250,
      tier: 'Silver',
      availableRewards: [],
    };
    
    return { success: true, data: mockRewards };
  }

  async claimReward(rewardId: string): Promise<ApiResponse<any>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, data: { rewardId, claimed: true } };
  }

  // Analytics and Tracking
  async trackEvent(eventName: string, properties: any): Promise<void> {
    try {
      // In a real app, this would send analytics data
      console.log('Analytics:', eventName, properties);
    } catch (error) {
      console.warn('Analytics tracking failed:', error);
    }
  }
}

export default new ApiService();