import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import ApiService from '../../services/api';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface User {
  id: string;
  name: string;
  phone: string;
  email?: string;
  avatar?: string;
  loyaltyPoints: number;
  membershipTier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  phoneVerificationSent: boolean;
  loginAttempts: number;
  lastLoginAttempt: number | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
  phoneVerificationSent: false,
  loginAttempts: 0,
  lastLoginAttempt: null,
};

// Async thunks
export const sendOTP = createAsyncThunk(
  'auth/sendOTP',
  async (phone: string, { rejectWithValue }) => {
    try {
      // In a real app, this would call the actual API
      // For now, simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to send OTP');
    }
  }
);

export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ phone, otp }: { phone: string; otp: string }, { rejectWithValue }) => {
    try {
      const response = await ApiService.login(phone, otp);
      
      if (!response.success) {
        return rejectWithValue(response.error || 'Invalid OTP');
      }
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Verification failed');
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      // In a real app, this would refresh the token
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        return rejectWithValue('No token found');
      }
      return { token };
    } catch (error) {
      return rejectWithValue('Token refresh failed');
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { dispatch }) => {
    await ApiService.logout();
    dispatch(resetAuth());
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    resetAuth: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.error = null;
      state.phoneVerificationSent = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
    incrementLoyaltyPoints: (state, action: PayloadAction<number>) => {
      if (state.user) {
        state.user.loyaltyPoints += action.payload;
        
        // Auto-upgrade membership tier
        if (state.user.loyaltyPoints >= 10000 && state.user.membershipTier !== 'Platinum') {
          state.user.membershipTier = 'Platinum';
        } else if (state.user.loyaltyPoints >= 5000 && state.user.membershipTier === 'Bronze') {
          state.user.membershipTier = 'Gold';
        } else if (state.user.loyaltyPoints >= 2000 && state.user.membershipTier === 'Bronze') {
          state.user.membershipTier = 'Silver';
        }
      }
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
      state.lastLoginAttempt = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Send OTP
      .addCase(sendOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendOTP.fulfilled, (state) => {
        state.loading = false;
        state.phoneVerificationSent = true;
        state.error = null;
      })
      .addCase(sendOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.phoneVerificationSent = false;
      })
      
      // Verify OTP
      .addCase(verifyOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.error = null;
        state.phoneVerificationSent = false;
        state.loginAttempts = 0;
        state.lastLoginAttempt = null;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;
        state.lastLoginAttempt = Date.now();
        
        // Lock account for 5 minutes after 5 failed attempts
        if (state.loginAttempts >= 5) {
          state.error = 'Too many failed attempts. Please try again in 5 minutes.';
        }
      })
      
      // Refresh Token
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      });
  },
});

export const {
  resetAuth,
  clearError,
  updateUser,
  incrementLoyaltyPoints,
  resetLoginAttempts,
} = authSlice.actions;

export default authSlice.reducer;