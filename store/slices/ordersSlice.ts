import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import ApiService from '../../services/api';

interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  unit: string;
  brand: string;
}

interface DeliveryAddress {
  id: string;
  type: 'home' | 'work' | 'other';
  name: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  landmark?: string;
  city: string;
  state: string;
  pincode: string;
  isDefault: boolean;
}

interface Order {
  id: string;
  orderNumber: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'out_for_delivery' | 'delivered' | 'cancelled' | 'refunded';
  items: OrderItem[];
  totalAmount: number;
  discountAmount: number;
  deliveryFee: number;
  taxes: number;
  finalAmount: number;
  paymentMethod: 'cod' | 'card' | 'upi' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  deliveryAddress: DeliveryAddress;
  estimatedDeliveryTime: string;
  actualDeliveryTime?: string;
  placedAt: string;
  updatedAt: string;
  deliveryInstructions?: string;
  promoCode?: string;
  loyaltyPointsUsed: number;
  loyaltyPointsEarned: number;
  rating?: number;
  review?: string;
  refundReason?: string;
  trackingUpdates: Array<{
    status: string;
    message: string;
    timestamp: string;
    location?: string;
  }>;
  invoice?: {
    url: string;
    number: string;
  };
}

interface OrdersState {
  orders: Order[];
  currentOrder: Order | null;
  orderHistory: Order[];
  activeOrders: Order[];
  addresses: DeliveryAddress[];
  selectedAddress: DeliveryAddress | null;
  checkoutData: {
    items: OrderItem[];
    subtotal: number;
    deliveryFee: number;
    taxes: number;
    discount: number;
    total: number;
    paymentMethod?: string;
    deliveryAddress?: DeliveryAddress;
    deliverySlot?: string;
    promoCode?: string;
    loyaltyPointsToUse: number;
    specialInstructions?: string;
  } | null;
  trackingData: {
    orderId: string;
    currentStatus: string;
    estimatedTime: string;
    deliveryAgent?: {
      name: string;
      phone: string;
      photo?: string;
      rating: number;
    };
    liveLocation?: {
      latitude: number;
      longitude: number;
      timestamp: string;
    };
  } | null;
  orderNotifications: Array<{
    id: string;
    orderId: string;
    type: 'status_update' | 'delivery_reminder' | 'payment_reminder';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
  }>;
  reorderHistory: Array<{
    orderId: string;
    frequency: number;
    lastOrdered: string;
  }>;
  favouriteItems: OrderItem[];
  loading: {
    orders: boolean;
    checkout: boolean;
    tracking: boolean;
    addresses: boolean;
  };
  error: string | null;
}

const initialState: OrdersState = {
  orders: [],
  currentOrder: null,
  orderHistory: [],
  activeOrders: [],
  addresses: [],
  selectedAddress: null,
  checkoutData: null,
  trackingData: null,
  orderNotifications: [],
  reorderHistory: [],
  favouriteItems: [],
  loading: {
    orders: false,
    checkout: false,
    tracking: false,
    addresses: false,
  },
  error: null,
};

// Async thunks
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.getOrders();
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch orders');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch orders');
    }
  }
);

export const fetchOrderDetails = createAsyncThunk(
  'orders/fetchOrderDetails',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.getOrderDetails(orderId);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch order details');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch order details');
    }
  }
);

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async (orderData: any, { rejectWithValue }) => {
    try {
      const response = await ApiService.createOrder(orderData);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to create order');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create order');
    }
  }
);

export const trackOrder = createAsyncThunk(
  'orders/trackOrder',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest(`/orders/${orderId}/track`);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to track order');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to track order');
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'orders/cancelOrder',
  async ({ orderId, reason }: { orderId: string; reason: string }, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest(`/orders/${orderId}/cancel`, {
        method: 'POST',
        body: JSON.stringify({ reason }),
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to cancel order');
      }
      return { orderId, ...response.data };
    } catch (error) {
      return rejectWithValue('Failed to cancel order');
    }
  }
);

export const rateOrder = createAsyncThunk(
  'orders/rateOrder',
  async ({ orderId, rating, review }: { orderId: string; rating: number; review?: string }, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest(`/orders/${orderId}/rate`, {
        method: 'POST',
        body: JSON.stringify({ rating, review }),
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to rate order');
      }
      return { orderId, rating, review };
    } catch (error) {
      return rejectWithValue('Failed to rate order');
    }
  }
);

export const fetchAddresses = createAsyncThunk(
  'orders/fetchAddresses',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.getAddresses();
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch addresses');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to fetch addresses');
    }
  }
);

export const addAddress = createAsyncThunk(
  'orders/addAddress',
  async (addressData: Omit<DeliveryAddress, 'id'>, { rejectWithValue }) => {
    try {
      const response = await ApiService.addAddress(addressData);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to add address');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to add address');
    }
  }
);

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setSelectedAddress: (state, action: PayloadAction<DeliveryAddress | null>) => {
      state.selectedAddress = action.payload;
    },

    initializeCheckout: (state, action: PayloadAction<{ items: OrderItem[]; subtotal: number }>) => {
      const { items, subtotal } = action.payload;
      state.checkoutData = {
        items,
        subtotal,
        deliveryFee: subtotal >= 299 ? 0 : 29,
        taxes: Math.round(subtotal * 0.05),
        discount: 0,
        total: subtotal + (subtotal >= 299 ? 0 : 29) + Math.round(subtotal * 0.05),
        loyaltyPointsToUse: 0,
      };
    },

    updateCheckoutData: (state, action: PayloadAction<Partial<OrdersState['checkoutData']>>) => {
      if (state.checkoutData) {
        state.checkoutData = { ...state.checkoutData, ...action.payload };
        
        // Recalculate total
        const subtotal = state.checkoutData.subtotal;
        const deliveryFee = state.checkoutData.deliveryFee || 0;
        const taxes = state.checkoutData.taxes || 0;
        const discount = state.checkoutData.discount || 0;
        const loyaltyDiscount = Math.floor((state.checkoutData.loyaltyPointsToUse || 0) / 10);
        
        state.checkoutData.total = subtotal + deliveryFee + taxes - discount - loyaltyDiscount;
      }
    },

    addToFavourites: (state, action: PayloadAction<OrderItem>) => {
      const item = action.payload;
      const exists = state.favouriteItems.find(fav => fav.productId === item.productId);
      if (!exists) {
        state.favouriteItems.push(item);
      }
    },

    removeFromFavourites: (state, action: PayloadAction<string>) => {
      state.favouriteItems = state.favouriteItems.filter(item => item.productId !== action.payload);
    },

    addOrderNotification: (state, action: PayloadAction<Omit<OrdersState['orderNotifications'][0], 'id' | 'timestamp' | 'read'>>) => {
      const notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.orderNotifications.unshift(notification);
      
      // Keep only last 50 notifications
      state.orderNotifications = state.orderNotifications.slice(0, 50);
    },

    markNotificationRead: (state, action: PayloadAction<string>) => {
      const notification = state.orderNotifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },

    updateOrderStatus: (state, action: PayloadAction<{ orderId: string; status: Order['status']; message?: string }>) => {
      const { orderId, status, message } = action.payload;
      
      // Update in orders array
      const order = state.orders.find(o => o.id === orderId);
      if (order) {
        order.status = status;
        order.updatedAt = new Date().toISOString();
        
        // Add tracking update
        order.trackingUpdates.push({
          status,
          message: message || `Order ${status.replace('_', ' ')}`,
          timestamp: new Date().toISOString(),
        });
      }

      // Update active orders
      state.activeOrders = state.orders.filter(o => !['delivered', 'cancelled', 'refunded'].includes(o.status));
      
      // Update order history
      state.orderHistory = state.orders.filter(o => ['delivered', 'cancelled', 'refunded'].includes(o.status));
    },

    updateReorderHistory: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      const existing = state.reorderHistory.find(r => r.orderId === orderId);
      
      if (existing) {
        existing.frequency += 1;
        existing.lastOrdered = new Date().toISOString();
      } else {
        state.reorderHistory.push({
          orderId,
          frequency: 1,
          lastOrdered: new Date().toISOString(),
        });
      }
    },

    clearCheckoutData: (state) => {
      state.checkoutData = null;
    },

    clearError: (state) => {
      state.error = null;
    },

    setCurrentOrder: (state, action: PayloadAction<Order | null>) => {
      state.currentOrder = action.payload;
    },

    updateLiveTracking: (state, action: PayloadAction<{ orderId: string; location: { latitude: number; longitude: number } }>) => {
      const { orderId, location } = action.payload;
      if (state.trackingData && state.trackingData.orderId === orderId) {
        state.trackingData.liveLocation = {
          ...location,
          timestamp: new Date().toISOString(),
        };
      }
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch Orders
      .addCase(fetchOrders.pending, (state) => {
        state.loading.orders = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.loading.orders = false;
        state.orders = action.payload;
        state.activeOrders = action.payload.filter(o => !['delivered', 'cancelled', 'refunded'].includes(o.status));
        state.orderHistory = action.payload.filter(o => ['delivered', 'cancelled', 'refunded'].includes(o.status));
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading.orders = false;
        state.error = action.payload as string;
      })

      // Fetch Order Details
      .addCase(fetchOrderDetails.fulfilled, (state, action) => {
        state.currentOrder = action.payload;
      })

      // Create Order
      .addCase(createOrder.pending, (state) => {
        state.loading.checkout = true;
        state.error = null;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.loading.checkout = false;
        state.currentOrder = action.payload;
        state.orders.unshift(action.payload);
        state.activeOrders.unshift(action.payload);
        state.checkoutData = null;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.loading.checkout = false;
        state.error = action.payload as string;
      })

      // Track Order
      .addCase(trackOrder.pending, (state) => {
        state.loading.tracking = true;
      })
      .addCase(trackOrder.fulfilled, (state, action) => {
        state.loading.tracking = false;
        state.trackingData = action.payload;
      })
      .addCase(trackOrder.rejected, (state, action) => {
        state.loading.tracking = false;
        state.error = action.payload as string;
      })

      // Cancel Order
      .addCase(cancelOrder.fulfilled, (state, action) => {
        const { orderId } = action.payload;
        const order = state.orders.find(o => o.id === orderId);
        if (order) {
          order.status = 'cancelled';
          order.updatedAt = new Date().toISOString();
        }
        state.activeOrders = state.activeOrders.filter(o => o.id !== orderId);
        state.orderHistory = state.orders.filter(o => ['delivered', 'cancelled', 'refunded'].includes(o.status));
      })

      // Rate Order
      .addCase(rateOrder.fulfilled, (state, action) => {
        const { orderId, rating, review } = action.payload;
        const order = state.orders.find(o => o.id === orderId);
        if (order) {
          order.rating = rating;
          order.review = review;
        }
      })

      // Fetch Addresses
      .addCase(fetchAddresses.pending, (state) => {
        state.loading.addresses = true;
      })
      .addCase(fetchAddresses.fulfilled, (state, action) => {
        state.loading.addresses = false;
        state.addresses = action.payload;
        const defaultAddress = action.payload.find(addr => addr.isDefault);
        if (defaultAddress) {
          state.selectedAddress = defaultAddress;
        }
      })
      .addCase(fetchAddresses.rejected, (state, action) => {
        state.loading.addresses = false;
        state.error = action.payload as string;
      })

      // Add Address
      .addCase(addAddress.fulfilled, (state, action) => {
        state.addresses.push(action.payload);
        if (action.payload.isDefault) {
          state.selectedAddress = action.payload;
        }
      });
  },
});

export const {
  setSelectedAddress,
  initializeCheckout,
  updateCheckoutData,
  addToFavourites,
  removeFromFavourites,
  addOrderNotification,
  markNotificationRead,
  updateOrderStatus,
  updateReorderHistory,
  clearCheckoutData,
  clearError,
  setCurrentOrder,
  updateLiveTracking,
} = ordersSlice.actions;

export default ordersSlice.reducer;