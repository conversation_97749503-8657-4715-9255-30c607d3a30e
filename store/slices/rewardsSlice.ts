import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import ApiService from '../../services/api';

interface Reward {
  id: string;
  type: 'discount' | 'free_delivery' | 'cashback' | 'product' | 'points';
  title: string;
  description: string;
  value: number;
  pointsCost: number;
  validUntil: string;
  minOrderValue?: number;
  maxDiscount?: number;
  category?: string;
  brand?: string;
  image?: string;
  termsAndConditions: string[];
  redemptionCount: number;
  maxRedemptions?: number;
  isPersonalized: boolean;
  popularityScore: number;
  tags: string[];
}

interface CouponCode {
  id: string;
  code: string;
  type: 'percentage' | 'fixed' | 'free_shipping' | 'bogo';
  value: number;
  minOrderValue: number;
  maxDiscount?: number;
  validFrom: string;
  validUntil: string;
  usageLimit: number;
  usedCount: number;
  isFirstTimeUser: boolean;
  categories?: string[];
  brands?: string[];
  description: string;
  termsAndConditions: string[];
}

interface LoyaltyTier {
  name: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  minPoints: number;
  benefits: string[];
  multiplier: number;
  color: string;
  icon: string;
}

interface SpecialOffer {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  type: 'flash_sale' | 'bulk_discount' | 'category_offer' | 'brand_offer' | 'seasonal';
  discountType: 'percentage' | 'fixed' | 'bogo';
  discountValue: number;
  minOrderValue?: number;
  maxDiscount?: number;
  validFrom: string;
  validUntil: string;
  targetAudience: 'all' | 'new_users' | 'premium' | 'inactive' | 'high_value';
  products?: string[];
  categories?: string[];
  brands?: string[];
  priority: number;
  isActive: boolean;
  viewCount: number;
  redemptionCount: number;
  tags: string[];
}

interface SpinWheelPrize {
  id: string;
  type: 'points' | 'discount' | 'free_delivery' | 'cashback' | 'product';
  name: string;
  value: number;
  probability: number;
  color: string;
  icon: string;
}

interface DailyCheckin {
  day: number;
  points: number;
  bonus?: {
    type: 'multiplier' | 'extra_points' | 'free_delivery';
    value: number;
  };
  claimed: boolean;
  claimedAt?: string;
}

interface ReferralProgram {
  referrerReward: {
    points: number;
    cashback: number;
    discount?: number;
  };
  refereeReward: {
    points: number;
    discount: number;
    freeDelivery: boolean;
  };
  minimumOrderValue: number;
  maxReferrals: number;
  validityDays: number;
}

interface RewardsState {
  availableRewards: Reward[];
  redeemedRewards: Reward[];
  coupons: CouponCode[];
  appliedCoupons: string[];
  specialOffers: SpecialOffer[];
  loyaltyTiers: LoyaltyTier[];
  currentTier: LoyaltyTier | null;
  loyaltyPoints: {
    available: number;
    earned: number;
    redeemed: number;
    expiring: Array<{
      points: number;
      expiryDate: string;
    }>;
  };
  spinWheel: {
    prizes: SpinWheelPrize[];
    available: boolean;
    lastSpin?: string;
    nextAvailable?: string;
    spinsToday: number;
    maxSpinsPerDay: number;
  };
  dailyCheckin: {
    streak: number;
    maxStreak: number;
    currentWeek: DailyCheckin[];
    lastCheckin?: string;
    weeklyBonus: {
      available: boolean;
      points: number;
    };
  };
  referralProgram: ReferralProgram;
  userReferrals: {
    code: string;
    totalReferred: number;
    successfulReferrals: number;
    pendingRewards: number;
    totalEarned: number;
    referredUsers: Array<{
      name: string;
      joinedAt: string;
      firstOrderCompleted: boolean;
      rewardEarned: number;
    }>;
  };
  notifications: Array<{
    id: string;
    type: 'reward_earned' | 'points_expiring' | 'new_offer' | 'tier_upgrade';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
    actionable: boolean;
    action?: {
      type: 'redeem' | 'view_offer' | 'use_points';
      data: any;
    };
  }>;
  gamification: {
    level: number;
    xp: number;
    xpToNextLevel: number;
    achievements: Array<{
      id: string;
      name: string;
      description: string;
      icon: string;
      unlocked: boolean;
      unlockedAt?: string;
      progress: number;
      maxProgress: number;
    }>;
    badges: Array<{
      id: string;
      name: string;
      description: string;
      icon: string;
      color: string;
      earnedAt: string;
    }>;
  };
  seasonalEvents: Array<{
    id: string;
    name: string;
    description: string;
    startDate: string;
    endDate: string;
    rewards: string[];
    challenges: Array<{
      id: string;
      title: string;
      description: string;
      target: number;
      progress: number;
      reward: number;
      completed: boolean;
    }>;
    leaderboard?: Array<{
      rank: number;
      userId: string;
      name: string;
      score: number;
    }>;
  }>;
  loading: {
    rewards: boolean;
    coupons: boolean;
    redeeming: boolean;
    spinning: boolean;
  };
  error: string | null;
}

const initialLoyaltyTiers: LoyaltyTier[] = [
  {
    name: 'Bronze',
    minPoints: 0,
    benefits: ['Basic rewards', 'Birthday discount'],
    multiplier: 1,
    color: '#CD7F32',
    icon: '🥉',
  },
  {
    name: 'Silver',
    minPoints: 2000,
    benefits: ['5% extra discount', 'Free delivery on orders above ₹199', 'Priority support'],
    multiplier: 1.2,
    color: '#C0C0C0',
    icon: '🥈',
  },
  {
    name: 'Gold',
    minPoints: 5000,
    benefits: ['10% extra discount', 'Free delivery on all orders', 'Early access to sales', 'Personal shopper'],
    multiplier: 1.5,
    color: '#FFD700',
    icon: '🥇',
  },
  {
    name: 'Platinum',
    minPoints: 10000,
    benefits: ['15% extra discount', 'Free express delivery', 'Exclusive products', 'VIP support', 'Monthly surprise gifts'],
    multiplier: 2,
    color: '#E5E4E2',
    icon: '💎',
  },
];

const initialState: RewardsState = {
  availableRewards: [],
  redeemedRewards: [],
  coupons: [],
  appliedCoupons: [],
  specialOffers: [],
  loyaltyTiers: initialLoyaltyTiers,
  currentTier: initialLoyaltyTiers[0],
  loyaltyPoints: {
    available: 0,
    earned: 0,
    redeemed: 0,
    expiring: [],
  },
  spinWheel: {
    prizes: [
      { id: '1', type: 'points', name: '50 Points', value: 50, probability: 30, color: '#4CAF50', icon: '⭐' },
      { id: '2', type: 'discount', name: '10% Off', value: 10, probability: 25, color: '#FF9800', icon: '🎯' },
      { id: '3', type: 'free_delivery', name: 'Free Delivery', value: 1, probability: 20, color: '#2196F3', icon: '🚚' },
      { id: '4', type: 'points', name: '100 Points', value: 100, probability: 15, color: '#9C27B0', icon: '💎' },
      { id: '5', type: 'cashback', name: '₹25 Cashback', value: 25, probability: 8, color: '#F44336', icon: '💰' },
      { id: '6', type: 'discount', name: '25% Off', value: 25, probability: 2, color: '#FF5722', icon: '🎉' },
    ],
    available: true,
    spinsToday: 0,
    maxSpinsPerDay: 3,
  },
  dailyCheckin: {
    streak: 0,
    maxStreak: 0,
    currentWeek: [
      { day: 1, points: 10, claimed: false },
      { day: 2, points: 15, claimed: false },
      { day: 3, points: 20, claimed: false },
      { day: 4, points: 25, claimed: false },
      { day: 5, points: 30, claimed: false },
      { day: 6, points: 40, claimed: false },
      { day: 7, points: 100, bonus: { type: 'multiplier', value: 2 }, claimed: false },
    ],
    weeklyBonus: {
      available: false,
      points: 200,
    },
  },
  referralProgram: {
    referrerReward: {
      points: 500,
      cashback: 100,
      discount: 10,
    },
    refereeReward: {
      points: 200,
      discount: 15,
      freeDelivery: true,
    },
    minimumOrderValue: 299,
    maxReferrals: 10,
    validityDays: 30,
  },
  userReferrals: {
    code: '',
    totalReferred: 0,
    successfulReferrals: 0,
    pendingRewards: 0,
    totalEarned: 0,
    referredUsers: [],
  },
  notifications: [],
  gamification: {
    level: 1,
    xp: 0,
    xpToNextLevel: 100,
    achievements: [],
    badges: [],
  },
  seasonalEvents: [],
  loading: {
    rewards: false,
    coupons: false,
    redeeming: false,
    spinning: false,
  },
  error: null,
};

// Async thunks
export const fetchRewards = createAsyncThunk(
  'rewards/fetchRewards',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/rewards');
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch rewards');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to fetch rewards');
    }
  }
);

export const redeemReward = createAsyncThunk(
  'rewards/redeemReward',
  async ({ rewardId, pointsCost }: { rewardId: string; pointsCost: number }, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/rewards/redeem', {
        method: 'POST',
        body: JSON.stringify({ rewardId }),
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to redeem reward');
      }
      return { rewardId, pointsCost, ...response.data };
    } catch (error) {
      return rejectWithValue('Failed to redeem reward');
    }
  }
);

export const spinWheel = createAsyncThunk(
  'rewards/spinWheel',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { rewards: RewardsState };
      if (!state.rewards.spinWheel.available) {
        return rejectWithValue('Spin wheel not available');
      }
      
      const response = await ApiService.makeRequest('/rewards/spin-wheel', {
        method: 'POST',
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to spin wheel');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to spin wheel');
    }
  }
);

export const dailyCheckin = createAsyncThunk(
  'rewards/dailyCheckin',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/rewards/daily-checkin', {
        method: 'POST',
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to check in');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to check in');
    }
  }
);

const rewardsSlice = createSlice({
  name: 'rewards',
  initialState,
  reducers: {
    addLoyaltyPoints: (state, action: PayloadAction<{ points: number; source: string }>) => {
      const { points, source } = action.payload;
      state.loyaltyPoints.available += points;
      state.loyaltyPoints.earned += points;
      
      // Check for tier upgrade
      const newTier = state.loyaltyTiers
        .slice()
        .reverse()
        .find(tier => state.loyaltyPoints.available >= tier.minPoints);
      
      if (newTier && newTier.name !== state.currentTier?.name) {
        state.currentTier = newTier;
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'tier_upgrade',
          title: 'Tier Upgraded!',
          message: `Congratulations! You've been upgraded to ${newTier.name} tier.`,
          timestamp: new Date().toISOString(),
          read: false,
          actionable: false,
        });
      }
      
      // Add notification
      state.notifications.unshift({
        id: Date.now().toString(),
        type: 'reward_earned',
        title: 'Points Earned!',
        message: `You earned ${points} loyalty points from ${source}.`,
        timestamp: new Date().toISOString(),
        read: false,
        actionable: false,
      });
    },

    deductLoyaltyPoints: (state, action: PayloadAction<number>) => {
      const points = action.payload;
      state.loyaltyPoints.available = Math.max(0, state.loyaltyPoints.available - points);
      state.loyaltyPoints.redeemed += points;
    },

    applyCoupon: (state, action: PayloadAction<string>) => {
      const couponCode = action.payload;
      if (!state.appliedCoupons.includes(couponCode)) {
        state.appliedCoupons.push(couponCode);
      }
    },

    removeCoupon: (state, action: PayloadAction<string>) => {
      state.appliedCoupons = state.appliedCoupons.filter(code => code !== action.payload);
    },

    markNotificationRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },

    clearNotifications: (state) => {
      state.notifications = state.notifications.filter(n => !n.read);
    },

    updateGamificationLevel: (state, action: PayloadAction<number>) => {
      const xp = action.payload;
      state.gamification.xp += xp;
      
      while (state.gamification.xp >= state.gamification.xpToNextLevel) {
        state.gamification.xp -= state.gamification.xpToNextLevel;
        state.gamification.level += 1;
        state.gamification.xpToNextLevel = Math.floor(state.gamification.xpToNextLevel * 1.5);
        
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'tier_upgrade',
          title: 'Level Up!',
          message: `Congratulations! You reached level ${state.gamification.level}!`,
          timestamp: new Date().toISOString(),
          read: false,
          actionable: false,
        });
      }
    },

    unlockAchievement: (state, action: PayloadAction<string>) => {
      const achievementId = action.payload;
      const achievement = state.gamification.achievements.find(a => a.id === achievementId);
      if (achievement && !achievement.unlocked) {
        achievement.unlocked = true;
        achievement.unlockedAt = new Date().toISOString();
        
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'reward_earned',
          title: 'Achievement Unlocked!',
          message: `${achievement.name} - ${achievement.description}`,
          timestamp: new Date().toISOString(),
          read: false,
          actionable: false,
        });
      }
    },

    addBadge: (state, action: PayloadAction<Omit<RewardsState['gamification']['badges'][0], 'earnedAt'>>) => {
      const badge = {
        ...action.payload,
        earnedAt: new Date().toISOString(),
      };
      state.gamification.badges.push(badge);
      
      state.notifications.unshift({
        id: Date.now().toString(),
        type: 'reward_earned',
        title: 'New Badge Earned!',
        message: `${badge.name} - ${badge.description}`,
        timestamp: new Date().toISOString(),
        read: false,
        actionable: false,
      });
    },

    updateReferralCode: (state, action: PayloadAction<string>) => {
      state.userReferrals.code = action.payload;
    },

    addReferralUser: (state, action: PayloadAction<{ name: string; firstOrderCompleted: boolean }>) => {
      const { name, firstOrderCompleted } = action.payload;
      state.userReferrals.totalReferred += 1;
      
      if (firstOrderCompleted) {
        state.userReferrals.successfulReferrals += 1;
        state.userReferrals.pendingRewards += state.referralProgram.referrerReward.points;
        state.userReferrals.totalEarned += state.referralProgram.referrerReward.cashback;
      }
      
      state.userReferrals.referredUsers.push({
        name,
        joinedAt: new Date().toISOString(),
        firstOrderCompleted,
        rewardEarned: firstOrderCompleted ? state.referralProgram.referrerReward.cashback : 0,
      });
    },

    setSpinWheelAvailable: (state, action: PayloadAction<boolean>) => {
      state.spinWheel.available = action.payload;
      if (!action.payload) {
        state.spinWheel.lastSpin = new Date().toISOString();
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        state.spinWheel.nextAvailable = tomorrow.toISOString();
      }
    },

    addExpiringPoints: (state, action: PayloadAction<{ points: number; expiryDate: string }>) => {
      state.loyaltyPoints.expiring.push(action.payload);
      
      // Notify about expiring points
      const daysUntilExpiry = Math.ceil(
        (new Date(action.payload.expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysUntilExpiry <= 7) {
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'points_expiring',
          title: 'Points Expiring Soon!',
          message: `${action.payload.points} points will expire in ${daysUntilExpiry} days.`,
          timestamp: new Date().toISOString(),
          read: false,
          actionable: true,
          action: {
            type: 'use_points',
            data: { points: action.payload.points },
          },
        });
      }
    },

    clearError: (state) => {
      state.error = null;
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch Rewards
      .addCase(fetchRewards.pending, (state) => {
        state.loading.rewards = true;
        state.error = null;
      })
      .addCase(fetchRewards.fulfilled, (state, action) => {
        state.loading.rewards = false;
        state.availableRewards = action.payload;
      })
      .addCase(fetchRewards.rejected, (state, action) => {
        state.loading.rewards = false;
        state.error = action.payload as string;
      })

      // Redeem Reward
      .addCase(redeemReward.pending, (state) => {
        state.loading.redeeming = true;
        state.error = null;
      })
      .addCase(redeemReward.fulfilled, (state, action) => {
        state.loading.redeeming = false;
        const { rewardId, pointsCost } = action.payload;
        
        // Move reward from available to redeemed
        const rewardIndex = state.availableRewards.findIndex(r => r.id === rewardId);
        if (rewardIndex !== -1) {
          const reward = state.availableRewards[rewardIndex];
          state.redeemedRewards.push(reward);
          state.availableRewards.splice(rewardIndex, 1);
        }
        
        // Deduct points
        state.loyaltyPoints.available -= pointsCost;
        state.loyaltyPoints.redeemed += pointsCost;
      })
      .addCase(redeemReward.rejected, (state, action) => {
        state.loading.redeeming = false;
        state.error = action.payload as string;
      })

      // Spin Wheel
      .addCase(spinWheel.pending, (state) => {
        state.loading.spinning = true;
        state.error = null;
      })
      .addCase(spinWheel.fulfilled, (state, action) => {
        state.loading.spinning = false;
        const prize = action.payload;
        
        state.spinWheel.spinsToday += 1;
        state.spinWheel.available = state.spinWheel.spinsToday < state.spinWheel.maxSpinsPerDay;
        
        // Add won prize to notifications
        state.notifications.unshift({
          id: Date.now().toString(),
          type: 'reward_earned',
          title: 'Spin Wheel Reward!',
          message: `You won: ${prize.name}!`,
          timestamp: new Date().toISOString(),
          read: false,
          actionable: false,
        });
      })
      .addCase(spinWheel.rejected, (state, action) => {
        state.loading.spinning = false;
        state.error = action.payload as string;
      })

      // Daily Check-in
      .addCase(dailyCheckin.fulfilled, (state, action) => {
        const { day, points, streak } = action.payload;
        
        if (state.dailyCheckin.currentWeek[day - 1]) {
          state.dailyCheckin.currentWeek[day - 1].claimed = true;
          state.dailyCheckin.currentWeek[day - 1].claimedAt = new Date().toISOString();
        }
        
        state.dailyCheckin.streak = streak;
        state.dailyCheckin.maxStreak = Math.max(state.dailyCheckin.maxStreak, streak);
        state.dailyCheckin.lastCheckin = new Date().toISOString();
        
        // Add points
        state.loyaltyPoints.available += points;
        state.loyaltyPoints.earned += points;
        
        // Check for weekly bonus
        const allClaimed = state.dailyCheckin.currentWeek.every(d => d.claimed);
        if (allClaimed && !state.dailyCheckin.weeklyBonus.available) {
          state.dailyCheckin.weeklyBonus.available = true;
        }
      });
  },
});

export const {
  addLoyaltyPoints,
  deductLoyaltyPoints,
  applyCoupon,
  removeCoupon,
  markNotificationRead,
  clearNotifications,
  updateGamificationLevel,
  unlockAchievement,
  addBadge,
  updateReferralCode,
  addReferralUser,
  setSpinWheelAvailable,
  addExpiringPoints,
  clearError,
} = rewardsSlice.actions;

export default rewardsSlice.reducer;