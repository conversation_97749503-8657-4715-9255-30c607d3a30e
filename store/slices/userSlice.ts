import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import ApiService from '../../services/api';

interface UserPreferences {
  language: 'en' | 'hi' | 'ta' | 'te' | 'bn';
  currency: 'INR' | 'USD';
  notifications: {
    orderUpdates: boolean;
    offers: boolean;
    recommendations: boolean;
    newsletter: boolean;
    sms: boolean;
    email: boolean;
    push: boolean;
  };
  privacy: {
    shareData: boolean;
    targetedAds: boolean;
    analytics: boolean;
  };
  delivery: {
    preferredTimeSlots: string[];
    deliveryInstructions: string;
    contactlessDelivery: boolean;
  };
  dietary: {
    vegetarian: boolean;
    vegan: boolean;
    glutenFree: boolean;
    organic: boolean;
    keto: boolean;
    diabetic: boolean;
  };
  shopping: {
    autoReorder: boolean;
    bulkDiscountAlerts: boolean;
    priceDropAlerts: boolean;
    stockAlerts: boolean;
  };
}

interface UserProfile {
  id: string;
  name: string;
  email?: string;
  phone: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  familyMembers?: Array<{
    name: string;
    relation: string;
    age?: number;
    preferences?: string[];
  }>;
  occupation?: string;
  income?: 'below_25k' | '25k_50k' | '50k_100k' | '100k_plus';
  householdSize: number;
  preferences: UserPreferences;
  loyaltyProgram: {
    tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
    points: number;
    totalEarned: number;
    totalRedeemed: number;
    nextTierPoints: number;
    benefits: string[];
    expiringPoints: Array<{
      points: number;
      expiryDate: string;
    }>;
  };
  subscription?: {
    type: 'premium' | 'plus';
    startDate: string;
    endDate: string;
    benefits: string[];
    autoRenew: boolean;
  };
  shoppingBehavior: {
    averageOrderValue: number;
    orderFrequency: 'daily' | 'weekly' | 'bi_weekly' | 'monthly';
    preferredCategories: string[];
    preferredBrands: string[];
    budgetRange: {
      min: number;
      max: number;
    };
    peakShoppingTimes: string[];
  };
  recommendations: {
    personal: string[];
    trending: string[];
    seasonal: string[];
    healthConscious: string[];
  };
}

interface UserAddress {
  id: string;
  type: 'home' | 'work' | 'other';
  name: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  landmark?: string;
  city: string;
  state: string;
  pincode: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isDefault: boolean;
  deliveryInstructions?: string;
}

interface PaymentMethod {
  id: string;
  type: 'card' | 'upi' | 'wallet' | 'bank_account';
  name: string;
  details: {
    last4?: string;
    brand?: string;
    upiId?: string;
    walletProvider?: string;
    bankName?: string;
  };
  isDefault: boolean;
  isVerified: boolean;
}

interface UserActivity {
  lastLogin: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  favoriteCategories: Array<{
    category: string;
    orderCount: number;
    totalSpent: number;
  }>;
  monthlyStats: Array<{
    month: string;
    orders: number;
    spent: number;
  }>;
  streaks: {
    current: number;
    longest: number;
    type: 'daily' | 'weekly';
  };
}

interface UserState {
  profile: UserProfile | null;
  addresses: UserAddress[];
  paymentMethods: PaymentMethod[];
  activity: UserActivity | null;
  referralCode: string;
  referralStats: {
    totalReferred: number;
    successfulReferrals: number;
    pendingRewards: number;
    totalEarned: number;
  };
  savedLists: Array<{
    id: string;
    name: string;
    items: string[];
    createdAt: string;
    isPublic: boolean;
    sharedWith: string[];
  }>;
  recentSearches: string[];
  viewHistory: Array<{
    productId: string;
    timestamp: string;
    category: string;
  }>;
  compareProducts: string[];
  wishlist: string[];
  cart: {
    items: Array<{
      productId: string;
      quantity: number;
      addedAt: string;
    }>;
    savedForLater: Array<{
      productId: string;
      savedAt: string;
    }>;
  };
  socialConnections: Array<{
    platform: 'google' | 'facebook' | 'apple';
    id: string;
    name: string;
    email?: string;
    connectedAt: string;
  }>;
  deviceInfo: {
    deviceId: string;
    platform: 'ios' | 'android' | 'web';
    appVersion: string;
    lastActive: string;
    pushToken?: string;
  };
  securitySettings: {
    twoFactorEnabled: boolean;
    biometricEnabled: boolean;
    sessionTimeout: number;
    trustedDevices: string[];
  };
  loading: {
    profile: boolean;
    addresses: boolean;
    payments: boolean;
    activity: boolean;
  };
  error: string | null;
}

const initialPreferences: UserPreferences = {
  language: 'en',
  currency: 'INR',
  notifications: {
    orderUpdates: true,
    offers: true,
    recommendations: true,
    newsletter: false,
    sms: true,
    email: true,
    push: true,
  },
  privacy: {
    shareData: false,
    targetedAds: true,
    analytics: true,
  },
  delivery: {
    preferredTimeSlots: ['9-12', '18-21'],
    deliveryInstructions: '',
    contactlessDelivery: false,
  },
  dietary: {
    vegetarian: false,
    vegan: false,
    glutenFree: false,
    organic: false,
    keto: false,
    diabetic: false,
  },
  shopping: {
    autoReorder: false,
    bulkDiscountAlerts: true,
    priceDropAlerts: true,
    stockAlerts: true,
  },
};

const initialState: UserState = {
  profile: null,
  addresses: [],
  paymentMethods: [],
  activity: null,
  referralCode: '',
  referralStats: {
    totalReferred: 0,
    successfulReferrals: 0,
    pendingRewards: 0,
    totalEarned: 0,
  },
  savedLists: [],
  recentSearches: [],
  viewHistory: [],
  compareProducts: [],
  wishlist: [],
  cart: {
    items: [],
    savedForLater: [],
  },
  socialConnections: [],
  deviceInfo: {
    deviceId: '',
    platform: 'android',
    appVersion: '1.0.0',
    lastActive: new Date().toISOString(),
  },
  securitySettings: {
    twoFactorEnabled: false,
    biometricEnabled: false,
    sessionTimeout: 30,
    trustedDevices: [],
  },
  loading: {
    profile: false,
    addresses: false,
    payments: false,
    activity: false,
  },
  error: null,
};

// Async thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.getUserProfile();
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch profile');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch profile');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      const response = await ApiService.updateUserProfile(profileData);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to update profile');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }
);

export const generateReferralCode = createAsyncThunk(
  'user/generateReferralCode',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/user/referral/generate', {
        method: 'POST',
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to generate referral code');
      }
      return response.data.code;
    } catch (error) {
      return rejectWithValue('Failed to generate referral code');
    }
  }
);

export const validateReferralCode = createAsyncThunk(
  'user/validateReferralCode',
  async (code: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/user/referral/validate', {
        method: 'POST',
        body: JSON.stringify({ code }),
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Invalid referral code');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to validate referral code');
    }
  }
);

export const fetchUserActivity = createAsyncThunk(
  'user/fetchActivity',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/user/activity');
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch activity');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to fetch activity');
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    updatePreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      if (state.profile) {
        state.profile.preferences = { ...state.profile.preferences, ...action.payload };
      }
    },

    addToWishlist: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (!state.wishlist.includes(productId)) {
        state.wishlist.push(productId);
      }
    },

    removeFromWishlist: (state, action: PayloadAction<string>) => {
      state.wishlist = state.wishlist.filter(id => id !== action.payload);
    },

    addToCompare: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (!state.compareProducts.includes(productId) && state.compareProducts.length < 4) {
        state.compareProducts.push(productId);
      }
    },

    removeFromCompare: (state, action: PayloadAction<string>) => {
      state.compareProducts = state.compareProducts.filter(id => id !== action.payload);
    },

    clearCompareList: (state) => {
      state.compareProducts = [];
    },

    addToRecentSearches: (state, action: PayloadAction<string>) => {
      const query = action.payload.trim();
      if (query && !state.recentSearches.includes(query)) {
        state.recentSearches.unshift(query);
        state.recentSearches = state.recentSearches.slice(0, 10);
      }
    },

    clearRecentSearches: (state) => {
      state.recentSearches = [];
    },

    addToViewHistory: (state, action: PayloadAction<{ productId: string; category: string }>) => {
      const { productId, category } = action.payload;
      const existing = state.viewHistory.findIndex(item => item.productId === productId);
      
      if (existing !== -1) {
        state.viewHistory.splice(existing, 1);
      }
      
      state.viewHistory.unshift({
        productId,
        category,
        timestamp: new Date().toISOString(),
      });
      
      state.viewHistory = state.viewHistory.slice(0, 50);
    },

    createSavedList: (state, action: PayloadAction<{ name: string; items: string[]; isPublic?: boolean }>) => {
      const { name, items, isPublic = false } = action.payload;
      const newList = {
        id: Date.now().toString(),
        name,
        items,
        createdAt: new Date().toISOString(),
        isPublic,
        sharedWith: [],
      };
      state.savedLists.push(newList);
    },

    updateSavedList: (state, action: PayloadAction<{ id: string; updates: Partial<UserState['savedLists'][0]> }>) => {
      const { id, updates } = action.payload;
      const listIndex = state.savedLists.findIndex(list => list.id === id);
      if (listIndex !== -1) {
        state.savedLists[listIndex] = { ...state.savedLists[listIndex], ...updates };
      }
    },

    deleteSavedList: (state, action: PayloadAction<string>) => {
      state.savedLists = state.savedLists.filter(list => list.id !== action.payload);
    },

    addToSavedList: (state, action: PayloadAction<{ listId: string; productId: string }>) => {
      const { listId, productId } = action.payload;
      const list = state.savedLists.find(l => l.id === listId);
      if (list && !list.items.includes(productId)) {
        list.items.push(productId);
      }
    },

    removeFromSavedList: (state, action: PayloadAction<{ listId: string; productId: string }>) => {
      const { listId, productId } = action.payload;
      const list = state.savedLists.find(l => l.id === listId);
      if (list) {
        list.items = list.items.filter(id => id !== productId);
      }
    },

    addAddress: (state, action: PayloadAction<UserAddress>) => {
      state.addresses.push(action.payload);
    },

    updateAddress: (state, action: PayloadAction<{ id: string; updates: Partial<UserAddress> }>) => {
      const { id, updates } = action.payload;
      const addressIndex = state.addresses.findIndex(addr => addr.id === id);
      if (addressIndex !== -1) {
        state.addresses[addressIndex] = { ...state.addresses[addressIndex], ...updates };
      }
    },

    deleteAddress: (state, action: PayloadAction<string>) => {
      state.addresses = state.addresses.filter(addr => addr.id !== action.payload);
    },

    setDefaultAddress: (state, action: PayloadAction<string>) => {
      state.addresses.forEach(addr => {
        addr.isDefault = addr.id === action.payload;
      });
    },

    addPaymentMethod: (state, action: PayloadAction<PaymentMethod>) => {
      state.paymentMethods.push(action.payload);
    },

    updatePaymentMethod: (state, action: PayloadAction<{ id: string; updates: Partial<PaymentMethod> }>) => {
      const { id, updates } = action.payload;
      const methodIndex = state.paymentMethods.findIndex(method => method.id === id);
      if (methodIndex !== -1) {
        state.paymentMethods[methodIndex] = { ...state.paymentMethods[methodIndex], ...updates };
      }
    },

    deletePaymentMethod: (state, action: PayloadAction<string>) => {
      state.paymentMethods = state.paymentMethods.filter(method => method.id !== action.payload);
    },

    setDefaultPaymentMethod: (state, action: PayloadAction<string>) => {
      state.paymentMethods.forEach(method => {
        method.isDefault = method.id === action.payload;
      });
    },

    updateReferralStats: (state, action: PayloadAction<Partial<UserState['referralStats']>>) => {
      state.referralStats = { ...state.referralStats, ...action.payload };
    },

    updateLoyaltyPoints: (state, action: PayloadAction<{ points: number; type: 'earn' | 'redeem' }>) => {
      const { points, type } = action.payload;
      if (state.profile?.loyaltyProgram) {
        if (type === 'earn') {
          state.profile.loyaltyProgram.points += points;
          state.profile.loyaltyProgram.totalEarned += points;
        } else {
          state.profile.loyaltyProgram.points -= points;
          state.profile.loyaltyProgram.totalRedeemed += points;
        }
        
        // Check for tier upgrade
        const { points: currentPoints } = state.profile.loyaltyProgram;
        if (currentPoints >= 10000 && state.profile.loyaltyProgram.tier !== 'Platinum') {
          state.profile.loyaltyProgram.tier = 'Platinum';
        } else if (currentPoints >= 5000 && state.profile.loyaltyProgram.tier === 'Bronze') {
          state.profile.loyaltyProgram.tier = 'Gold';
        } else if (currentPoints >= 2000 && state.profile.loyaltyProgram.tier === 'Bronze') {
          state.profile.loyaltyProgram.tier = 'Silver';
        }
      }
    },

    updateDeviceInfo: (state, action: PayloadAction<Partial<UserState['deviceInfo']>>) => {
      state.deviceInfo = { ...state.deviceInfo, ...action.payload };
    },

    updateSecuritySettings: (state, action: PayloadAction<Partial<UserState['securitySettings']>>) => {
      state.securitySettings = { ...state.securitySettings, ...action.payload };
    },

    addSocialConnection: (state, action: PayloadAction<UserState['socialConnections'][0]>) => {
      const existing = state.socialConnections.findIndex(
        conn => conn.platform === action.payload.platform && conn.id === action.payload.id
      );
      if (existing === -1) {
        state.socialConnections.push(action.payload);
      }
    },

    removeSocialConnection: (state, action: PayloadAction<{ platform: string; id: string }>) => {
      const { platform, id } = action.payload;
      state.socialConnections = state.socialConnections.filter(
        conn => !(conn.platform === platform && conn.id === id)
      );
    },

    clearError: (state) => {
      state.error = null;
    },

    resetUserData: (state) => {
      return initialState;
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch User Profile
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading.profile = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading.profile = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading.profile = false;
        state.error = action.payload as string;
      })

      // Update User Profile
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload };
        }
      })

      // Generate Referral Code
      .addCase(generateReferralCode.fulfilled, (state, action) => {
        state.referralCode = action.payload;
      })

      // Fetch User Activity
      .addCase(fetchUserActivity.pending, (state) => {
        state.loading.activity = true;
      })
      .addCase(fetchUserActivity.fulfilled, (state, action) => {
        state.loading.activity = false;
        state.activity = action.payload;
      })
      .addCase(fetchUserActivity.rejected, (state, action) => {
        state.loading.activity = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  updatePreferences,
  addToWishlist,
  removeFromWishlist,
  addToCompare,
  removeFromCompare,
  clearCompareList,
  addToRecentSearches,
  clearRecentSearches,
  addToViewHistory,
  createSavedList,
  updateSavedList,
  deleteSavedList,
  addToSavedList,
  removeFromSavedList,
  addAddress,
  updateAddress,
  deleteAddress,
  setDefaultAddress,
  addPaymentMethod,
  updatePaymentMethod,
  deletePaymentMethod,
  setDefaultPaymentMethod,
  updateReferralStats,
  updateLoyaltyPoints,
  updateDeviceInfo,
  updateSecuritySettings,
  addSocialConnection,
  removeSocialConnection,
  clearError,
  resetUserData,
} = userSlice.actions;

export default userSlice.reducer;