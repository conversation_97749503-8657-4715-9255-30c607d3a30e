import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Modal {
  id: string;
  type: 'product_details' | 'cart_summary' | 'address_form' | 'payment_options' | 'confirmation' | 'error' | 'success';
  title?: string;
  content?: any;
  visible: boolean;
  closable?: boolean;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
}

interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
  timestamp: string;
}

interface LoadingState {
  global: boolean;
  components: {
    [key: string]: boolean;
  };
}

interface UIState {
  theme: 'light' | 'dark' | 'system';
  colorScheme: 'light' | 'dark';
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  screenDimensions: {
    width: number;
    height: number;
  };
  orientation: 'portrait' | 'landscape';
  statusBar: {
    style: 'light' | 'dark';
    backgroundColor: string;
    hidden: boolean;
  };
  navigation: {
    currentRoute: string;
    previousRoute?: string;
    params?: any;
    canGoBack: boolean;
  };
  modals: Modal[];
  toasts: Toast[];
  loading: LoadingState;
  bottomSheet: {
    visible: boolean;
    type?: 'filter' | 'sort' | 'share' | 'options' | 'image_picker';
    height?: number;
    content?: any;
    closable?: boolean;
  };
  keyboard: {
    visible: boolean;
    height: number;
  };
  connectivity: {
    isConnected: boolean;
    type: 'wifi' | 'cellular' | 'none';
    isInternetReachable: boolean;
  };
  permissions: {
    camera: 'granted' | 'denied' | 'undetermined';
    location: 'granted' | 'denied' | 'undetermined';
    notifications: 'granted' | 'denied' | 'undetermined';
    microphone: 'granted' | 'denied' | 'undetermined';
  };
  appState: 'active' | 'background' | 'inactive';
  splashScreen: {
    visible: boolean;
    animationComplete: boolean;
  };
  onboarding: {
    completed: boolean;
    currentStep: number;
    totalSteps: number;
    skippable: boolean;
  };
  search: {
    focused: boolean;
    query: string;
    suggestions: string[];
    recentSearches: string[];
    trending: string[];
  };
  filters: {
    visible: boolean;
    applied: {
      [key: string]: any;
    };
    available: {
      [key: string]: {
        label: string;
        type: 'select' | 'range' | 'checkbox' | 'radio';
        options?: string[];
        min?: number;
        max?: number;
      };
    };
  };
  sort: {
    visible: boolean;
    selected: string;
    options: Array<{
      id: string;
      label: string;
      order: 'asc' | 'desc';
    }>;
  };
  tabs: {
    activeTab: string;
    badges: {
      [key: string]: number;
    };
    hidden: boolean;
  };
  pullToRefresh: {
    refreshing: boolean;
    enabled: boolean;
  };
  infiniteScroll: {
    loading: boolean;
    hasMore: boolean;
    threshold: number;
  };
  animations: {
    enabled: boolean;
    reducedMotion: boolean;
    duration: 'fast' | 'normal' | 'slow';
  };
  accessibility: {
    screenReader: boolean;
    fontSize: 'small' | 'normal' | 'large' | 'extra_large';
    highContrast: boolean;
    reduceTransparency: boolean;
  };
  haptics: {
    enabled: boolean;
    intensity: 'light' | 'medium' | 'heavy';
  };
  voiceSearch: {
    active: boolean;
    listening: boolean;
    result?: string;
    error?: string;
  };
  cameraScanner: {
    active: boolean;
    type: 'barcode' | 'qr' | 'text';
    result?: string;
    error?: string;
  };
  locationServices: {
    enabled: boolean;
    currentLocation?: {
      latitude: number;
      longitude: number;
      accuracy: number;
      timestamp: string;
    };
    tracking: boolean;
  };
  performance: {
    fps: number;
    memoryUsage: number;
    renderTime: number;
    jsHeapSize: number;
  };
  debug: {
    enabled: boolean;
    showFPS: boolean;
    showMemory: boolean;
    showRenderTime: boolean;
    logLevel: 'none' | 'error' | 'warn' | 'info' | 'debug';
  };
}

const initialState: UIState = {
  theme: 'system',
  colorScheme: 'light',
  safeAreaInsets: {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  screenDimensions: {
    width: 375,
    height: 812,
  },
  orientation: 'portrait',
  statusBar: {
    style: 'dark',
    backgroundColor: '#FFFFFF',
    hidden: false,
  },
  navigation: {
    currentRoute: 'index',
    canGoBack: false,
  },
  modals: [],
  toasts: [],
  loading: {
    global: false,
    components: {},
  },
  bottomSheet: {
    visible: false,
    closable: true,
  },
  keyboard: {
    visible: false,
    height: 0,
  },
  connectivity: {
    isConnected: true,
    type: 'wifi',
    isInternetReachable: true,
  },
  permissions: {
    camera: 'undetermined',
    location: 'undetermined',
    notifications: 'undetermined',
    microphone: 'undetermined',
  },
  appState: 'active',
  splashScreen: {
    visible: true,
    animationComplete: false,
  },
  onboarding: {
    completed: false,
    currentStep: 0,
    totalSteps: 5,
    skippable: true,
  },
  search: {
    focused: false,
    query: '',
    suggestions: [],
    recentSearches: [],
    trending: [],
  },
  filters: {
    visible: false,
    applied: {},
    available: {},
  },
  sort: {
    visible: false,
    selected: 'popularity',
    options: [
      { id: 'popularity', label: 'Most Popular', order: 'desc' },
      { id: 'price_low', label: 'Price: Low to High', order: 'asc' },
      { id: 'price_high', label: 'Price: High to Low', order: 'desc' },
      { id: 'rating', label: 'Highest Rated', order: 'desc' },
      { id: 'newest', label: 'Newest First', order: 'desc' },
      { id: 'discount', label: 'Best Discount', order: 'desc' },
    ],
  },
  tabs: {
    activeTab: 'index',
    badges: {},
    hidden: false,
  },
  pullToRefresh: {
    refreshing: false,
    enabled: true,
  },
  infiniteScroll: {
    loading: false,
    hasMore: true,
    threshold: 100,
  },
  animations: {
    enabled: true,
    reducedMotion: false,
    duration: 'normal',
  },
  accessibility: {
    screenReader: false,
    fontSize: 'normal',
    highContrast: false,
    reduceTransparency: false,
  },
  haptics: {
    enabled: true,
    intensity: 'medium',
  },
  voiceSearch: {
    active: false,
    listening: false,
  },
  cameraScanner: {
    active: false,
    type: 'barcode',
  },
  locationServices: {
    enabled: false,
    tracking: false,
  },
  performance: {
    fps: 60,
    memoryUsage: 0,
    renderTime: 0,
    jsHeapSize: 0,
  },
  debug: {
    enabled: false,
    showFPS: false,
    showMemory: false,
    showRenderTime: false,
    logLevel: 'error',
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<UIState['theme']>) => {
      state.theme = action.payload;
    },

    setColorScheme: (state, action: PayloadAction<UIState['colorScheme']>) => {
      state.colorScheme = action.payload;
    },

    setSafeAreaInsets: (state, action: PayloadAction<UIState['safeAreaInsets']>) => {
      state.safeAreaInsets = action.payload;
    },

    setScreenDimensions: (state, action: PayloadAction<UIState['screenDimensions']>) => {
      state.screenDimensions = action.payload;
    },

    setOrientation: (state, action: PayloadAction<UIState['orientation']>) => {
      state.orientation = action.payload;
    },

    setStatusBar: (state, action: PayloadAction<Partial<UIState['statusBar']>>) => {
      state.statusBar = { ...state.statusBar, ...action.payload };
    },

    setNavigation: (state, action: PayloadAction<Partial<UIState['navigation']>>) => {
      if (action.payload.currentRoute && state.navigation.currentRoute !== action.payload.currentRoute) {
        state.navigation.previousRoute = state.navigation.currentRoute;
      }
      state.navigation = { ...state.navigation, ...action.payload };
    },

    showModal: (state, action: PayloadAction<Omit<Modal, 'visible'>>) => {
      const modal: Modal = { ...action.payload, visible: true };
      state.modals.push(modal);
    },

    hideModal: (state, action: PayloadAction<string>) => {
      const modalIndex = state.modals.findIndex(modal => modal.id === action.payload);
      if (modalIndex !== -1) {
        state.modals[modalIndex].visible = false;
      }
    },

    removeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter(modal => modal.id !== action.payload);
    },

    clearModals: (state) => {
      state.modals = [];
    },

    showToast: (state, action: PayloadAction<Omit<Toast, 'id' | 'timestamp'>>) => {
      const toast: Toast = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
      };
      state.toasts.push(toast);
    },

    hideToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },

    clearToasts: (state) => {
      state.toasts = [];
    },

    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },

    setComponentLoading: (state, action: PayloadAction<{ component: string; loading: boolean }>) => {
      const { component, loading } = action.payload;
      state.loading.components[component] = loading;
    },

    clearComponentLoading: (state) => {
      state.loading.components = {};
    },

    showBottomSheet: (state, action: PayloadAction<Omit<UIState['bottomSheet'], 'visible'>>) => {
      state.bottomSheet = { ...action.payload, visible: true };
    },

    hideBottomSheet: (state) => {
      state.bottomSheet.visible = false;
    },

    setKeyboard: (state, action: PayloadAction<UIState['keyboard']>) => {
      state.keyboard = action.payload;
    },

    setConnectivity: (state, action: PayloadAction<UIState['connectivity']>) => {
      state.connectivity = action.payload;
    },

    setPermission: (state, action: PayloadAction<{ type: keyof UIState['permissions']; status: UIState['permissions'][keyof UIState['permissions']] }>) => {
      const { type, status } = action.payload;
      state.permissions[type] = status;
    },

    setAppState: (state, action: PayloadAction<UIState['appState']>) => {
      state.appState = action.payload;
    },

    setSplashScreen: (state, action: PayloadAction<Partial<UIState['splashScreen']>>) => {
      state.splashScreen = { ...state.splashScreen, ...action.payload };
    },

    setOnboarding: (state, action: PayloadAction<Partial<UIState['onboarding']>>) => {
      state.onboarding = { ...state.onboarding, ...action.payload };
    },

    nextOnboardingStep: (state) => {
      if (state.onboarding.currentStep < state.onboarding.totalSteps - 1) {
        state.onboarding.currentStep += 1;
      }
    },

    previousOnboardingStep: (state) => {
      if (state.onboarding.currentStep > 0) {
        state.onboarding.currentStep -= 1;
      }
    },

    completeOnboarding: (state) => {
      state.onboarding.completed = true;
      state.onboarding.currentStep = state.onboarding.totalSteps;
    },

    setSearch: (state, action: PayloadAction<Partial<UIState['search']>>) => {
      state.search = { ...state.search, ...action.payload };
    },

    addRecentSearch: (state, action: PayloadAction<string>) => {
      const query = action.payload.trim();
      if (query && !state.search.recentSearches.includes(query)) {
        state.search.recentSearches.unshift(query);
        state.search.recentSearches = state.search.recentSearches.slice(0, 10);
      }
    },

    clearRecentSearches: (state) => {
      state.search.recentSearches = [];
    },

    showFilters: (state) => {
      state.filters.visible = true;
    },

    hideFilters: (state) => {
      state.filters.visible = false;
    },

    setFilters: (state, action: PayloadAction<UIState['filters']['applied']>) => {
      state.filters.applied = action.payload;
    },

    addFilter: (state, action: PayloadAction<{ key: string; value: any }>) => {
      const { key, value } = action.payload;
      state.filters.applied[key] = value;
    },

    removeFilter: (state, action: PayloadAction<string>) => {
      delete state.filters.applied[action.payload];
    },

    clearFilters: (state) => {
      state.filters.applied = {};
    },

    showSort: (state) => {
      state.sort.visible = true;
    },

    hideSort: (state) => {
      state.sort.visible = false;
    },

    setSort: (state, action: PayloadAction<string>) => {
      state.sort.selected = action.payload;
      state.sort.visible = false;
    },

    setActiveTab: (state, action: PayloadAction<string>) => {
      state.tabs.activeTab = action.payload;
    },

    setTabBadge: (state, action: PayloadAction<{ tab: string; count: number }>) => {
      const { tab, count } = action.payload;
      if (count > 0) {
        state.tabs.badges[tab] = count;
      } else {
        delete state.tabs.badges[tab];
      }
    },

    hideTabs: (state) => {
      state.tabs.hidden = true;
    },

    showTabs: (state) => {
      state.tabs.hidden = false;
    },

    setPullToRefresh: (state, action: PayloadAction<Partial<UIState['pullToRefresh']>>) => {
      state.pullToRefresh = { ...state.pullToRefresh, ...action.payload };
    },

    setInfiniteScroll: (state, action: PayloadAction<Partial<UIState['infiniteScroll']>>) => {
      state.infiniteScroll = { ...state.infiniteScroll, ...action.payload };
    },

    setAnimations: (state, action: PayloadAction<Partial<UIState['animations']>>) => {
      state.animations = { ...state.animations, ...action.payload };
    },

    setAccessibility: (state, action: PayloadAction<Partial<UIState['accessibility']>>) => {
      state.accessibility = { ...state.accessibility, ...action.payload };
    },

    setHaptics: (state, action: PayloadAction<Partial<UIState['haptics']>>) => {
      state.haptics = { ...state.haptics, ...action.payload };
    },

    setVoiceSearch: (state, action: PayloadAction<Partial<UIState['voiceSearch']>>) => {
      state.voiceSearch = { ...state.voiceSearch, ...action.payload };
    },

    setCameraScanner: (state, action: PayloadAction<Partial<UIState['cameraScanner']>>) => {
      state.cameraScanner = { ...state.cameraScanner, ...action.payload };
    },

    setLocationServices: (state, action: PayloadAction<Partial<UIState['locationServices']>>) => {
      state.locationServices = { ...state.locationServices, ...action.payload };
    },

    setPerformance: (state, action: PayloadAction<Partial<UIState['performance']>>) => {
      state.performance = { ...state.performance, ...action.payload };
    },

    setDebug: (state, action: PayloadAction<Partial<UIState['debug']>>) => {
      state.debug = { ...state.debug, ...action.payload };
    },
  },
});

export const {
  setTheme,
  setColorScheme,
  setSafeAreaInsets,
  setScreenDimensions,
  setOrientation,
  setStatusBar,
  setNavigation,
  showModal,
  hideModal,
  removeModal,
  clearModals,
  showToast,
  hideToast,
  clearToasts,
  setGlobalLoading,
  setComponentLoading,
  clearComponentLoading,
  showBottomSheet,
  hideBottomSheet,
  setKeyboard,
  setConnectivity,
  setPermission,
  setAppState,
  setSplashScreen,
  setOnboarding,
  nextOnboardingStep,
  previousOnboardingStep,
  completeOnboarding,
  setSearch,
  addRecentSearch,
  clearRecentSearches,
  showFilters,
  hideFilters,
  setFilters,
  addFilter,
  removeFilter,
  clearFilters,
  showSort,
  hideSort,
  setSort,
  setActiveTab,
  setTabBadge,
  hideTabs,
  showTabs,
  setPullToRefresh,
  setInfiniteScroll,
  setAnimations,
  setAccessibility,
  setHaptics,
  setVoiceSearch,
  setCameraScanner,
  setLocationServices,
  setPerformance,
  setDebug,
} = uiSlice.actions;

export default uiSlice.reducer;