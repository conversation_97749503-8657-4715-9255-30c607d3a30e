import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  unlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
  category: 'shopping' | 'social' | 'eco' | 'loyalty' | 'special';
}

interface Badge {
  id: string;
  name: string;
  icon: string;
  color: string;
  earnedAt: string;
  description: string;
}

interface Streak {
  type: 'daily_order' | 'weekly_shop' | 'eco_friendly' | 'referral';
  count: number;
  maxStreak: number;
  lastActivityDate: string;
  isActive: boolean;
  reward?: {
    points: number;
    discount?: number;
    freeDelivery?: boolean;
  };
}

interface LevelSystem {
  currentLevel: number;
  currentXP: number;
  xpToNextLevel: number;
  totalXP: number;
  levelName: string;
  perks: string[];
}

interface GamificationState {
  level: LevelSystem;
  totalPoints: number;
  achievements: Achievement[];
  badges: Badge[];
  streaks: { [key: string]: Streak };
  dailyChallenge: {
    id: string;
    title: string;
    description: string;
    target: number;
    progress: number;
    reward: number;
    completed: boolean;
    expiresAt: string;
  } | null;
  weeklyRewards: {
    week: number;
    claimed: boolean[];
    currentDay: number;
  };
  spinWheel: {
    available: boolean;
    lastSpinDate?: string;
    prizes: Array<{
      id: string;
      name: string;
      type: 'points' | 'discount' | 'free_delivery' | 'product';
      value: number;
      probability: number;
    }>;
  };
  referralProgram: {
    referralCode: string;
    referredUsers: number;
    pendingRewards: number;
    totalEarned: number;
  };
  notifications: Array<{
    id: string;
    type: 'achievement' | 'streak' | 'level_up' | 'reward';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
  }>;
  loading: boolean;
  error: string | null;
}

const initialState: GamificationState = {
  level: {
    currentLevel: 1,
    currentXP: 0,
    xpToNextLevel: 100,
    totalXP: 0,
    levelName: 'Grocery Newbie',
    perks: ['Welcome bonus', 'Basic rewards'],
  },
  totalPoints: 0,
  achievements: [
    {
      id: 'first_order',
      title: 'First Steps',
      description: 'Complete your first order',
      icon: '🛒',
      points: 50,
      unlocked: false,
      category: 'shopping',
    },
    {
      id: 'eco_warrior',
      title: 'Eco Warrior',
      description: 'Choose eco-friendly packaging 5 times',
      icon: '🌱',
      points: 100,
      unlocked: false,
      progress: 0,
      maxProgress: 5,
      category: 'eco',
    },
    {
      id: 'social_shopper',
      title: 'Social Shopper',
      description: 'Share your first shopping list',
      icon: '🤝',
      points: 75,
      unlocked: false,
      category: 'social',
    },
    {
      id: 'speed_shopper',
      title: 'Speed Shopper',
      description: 'Complete checkout in under 2 minutes',
      icon: '⚡',
      points: 125,
      unlocked: false,
      category: 'shopping',
    },
    {
      id: 'loyalty_master',
      title: 'Loyalty Master',
      description: 'Reach 30-day shopping streak',
      icon: '👑',
      points: 500,
      unlocked: false,
      progress: 0,
      maxProgress: 30,
      category: 'loyalty',
    },
  ],
  badges: [],
  streaks: {
    daily_order: {
      type: 'daily_order',
      count: 0,
      maxStreak: 0,
      lastActivityDate: '',
      isActive: false,
    },
    weekly_shop: {
      type: 'weekly_shop',
      count: 0,
      maxStreak: 0,
      lastActivityDate: '',
      isActive: false,
    },
    eco_friendly: {
      type: 'eco_friendly',
      count: 0,
      maxStreak: 0,
      lastActivityDate: '',
      isActive: false,
    },
  },
  dailyChallenge: null,
  weeklyRewards: {
    week: 1,
    claimed: [false, false, false, false, false, false, false],
    currentDay: 0,
  },
  spinWheel: {
    available: true,
    prizes: [
      { id: '1', name: '50 Points', type: 'points', value: 50, probability: 30 },
      { id: '2', name: '10% Off', type: 'discount', value: 10, probability: 25 },
      { id: '3', name: 'Free Delivery', type: 'free_delivery', value: 1, probability: 20 },
      { id: '4', name: '100 Points', type: 'points', value: 100, probability: 15 },
      { id: '5', name: '20% Off', type: 'discount', value: 20, probability: 8 },
      { id: '6', name: 'Free Product', type: 'product', value: 1, probability: 2 },
    ],
  },
  referralProgram: {
    referralCode: '',
    referredUsers: 0,
    pendingRewards: 0,
    totalEarned: 0,
  },
  notifications: [],
  loading: false,
  error: null,
};

// Async thunks
export const generateDailyChallenge = createAsyncThunk(
  'gamification/generateDailyChallenge',
  async () => {
    const challenges = [
      { title: 'Green Day', description: 'Buy 3 organic products', target: 3, reward: 100 },
      { title: 'Bulk Buyer', description: 'Spend over ₹500 in one order', target: 500, reward: 150 },
      { title: 'Early Bird', description: 'Place order before 10 AM', target: 1, reward: 75 },
      { title: 'Veggie Lover', description: 'Add 5 vegetables to cart', target: 5, reward: 80 },
    ];
    
    const randomChallenge = challenges[Math.floor(Math.random() * challenges.length)];
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return {
      id: `challenge_${Date.now()}`,
      ...randomChallenge,
      progress: 0,
      completed: false,
      expiresAt: tomorrow.toISOString(),
    };
  }
);

export const spinRewardWheel = createAsyncThunk(
  'gamification/spinRewardWheel',
  async (_, { getState, rejectWithValue }) => {
    const state = getState() as { gamification: GamificationState };
    
    if (!state.gamification.spinWheel.available) {
      return rejectWithValue('Spin wheel not available');
    }
    
    const prizes = state.gamification.spinWheel.prizes;
    const random = Math.random() * 100;
    let cumulativeProbability = 0;
    
    for (const prize of prizes) {
      cumulativeProbability += prize.probability;
      if (random <= cumulativeProbability) {
        return prize;
      }
    }
    
    return prizes[0]; // fallback
  }
);

const gamificationSlice = createSlice({
  name: 'gamification',
  initialState,
  reducers: {
    addXP: (state, action: PayloadAction<number>) => {
      const xp = action.payload;
      state.level.currentXP += xp;
      state.level.totalXP += xp;
      
      // Check for level up
      while (state.level.currentXP >= state.level.xpToNextLevel) {
        state.level.currentXP -= state.level.xpToNextLevel;
        state.level.currentLevel += 1;
        state.level.xpToNextLevel = Math.floor(state.level.xpToNextLevel * 1.5);
        
        // Update level name and perks
        const levelNames = [
          'Grocery Newbie', 'Smart Shopper', 'Bulk Buyer', 'Eco Champion',
          'Speed Runner', 'Loyalty Expert', 'Shopping Master', 'Grocery Guru'
        ];
        
        if (state.level.currentLevel <= levelNames.length) {
          state.level.levelName = levelNames[state.level.currentLevel - 1];
        }
        
        // Add level up notification
        state.notifications.unshift({
          id: `levelup_${Date.now()}`,
          type: 'level_up',
          title: 'Level Up!',
          message: `Congratulations! You reached level ${state.level.currentLevel}`,
          timestamp: new Date().toISOString(),
          read: false,
        });
      }
    },
    
    addPoints: (state, action: PayloadAction<number>) => {
      state.totalPoints += action.payload;
    },
    
    unlockAchievement: (state, action: PayloadAction<string>) => {
      const achievement = state.achievements.find(a => a.id === action.payload);
      if (achievement && !achievement.unlocked) {
        achievement.unlocked = true;
        achievement.unlockedAt = new Date().toISOString();
        state.totalPoints += achievement.points;
        
        state.notifications.unshift({
          id: `achievement_${Date.now()}`,
          type: 'achievement',
          title: 'Achievement Unlocked!',
          message: `${achievement.title} - ${achievement.description}`,
          timestamp: new Date().toISOString(),
          read: false,
        });
      }
    },
    
    updateAchievementProgress: (state, action: PayloadAction<{ id: string; progress: number }>) => {
      const { id, progress } = action.payload;
      const achievement = state.achievements.find(a => a.id === id);
      if (achievement && achievement.maxProgress) {
        achievement.progress = Math.min(progress, achievement.maxProgress);
        
        if (achievement.progress >= achievement.maxProgress && !achievement.unlocked) {
          achievement.unlocked = true;
          achievement.unlockedAt = new Date().toISOString();
          state.totalPoints += achievement.points;
          
          state.notifications.unshift({
            id: `achievement_${Date.now()}`,
            type: 'achievement',
            title: 'Achievement Unlocked!',
            message: `${achievement.title} - ${achievement.description}`,
            timestamp: new Date().toISOString(),
            read: false,
          });
        }
      }
    },
    
    updateStreak: (state, action: PayloadAction<{ type: keyof typeof state.streaks; increment: boolean }>) => {
      const { type, increment } = action.payload;
      const streak = state.streaks[type];
      
      if (increment) {
        streak.count += 1;
        streak.maxStreak = Math.max(streak.maxStreak, streak.count);
        streak.isActive = true;
        streak.lastActivityDate = new Date().toISOString();
        
        // Streak rewards
        if (streak.count % 7 === 0) {
          const reward = streak.count * 10;
          state.totalPoints += reward;
          
          state.notifications.unshift({
            id: `streak_${Date.now()}`,
            type: 'streak',
            title: 'Streak Bonus!',
            message: `${streak.count} day streak! Earned ${reward} points.`,
            timestamp: new Date().toISOString(),
            read: false,
          });
        }
      } else {
        streak.count = 0;
        streak.isActive = false;
      }
    },
    
    addBadge: (state, action: PayloadAction<Omit<Badge, 'earnedAt'>>) => {
      const newBadge: Badge = {
        ...action.payload,
        earnedAt: new Date().toISOString(),
      };
      state.badges.push(newBadge);
      
      state.notifications.unshift({
        id: `badge_${Date.now()}`,
        type: 'achievement',
        title: 'New Badge Earned!',
        message: `${newBadge.name} - ${newBadge.description}`,
        timestamp: new Date().toISOString(),
        read: false,
      });
    },
    
    updateDailyChallengeProgress: (state, action: PayloadAction<number>) => {
      if (state.dailyChallenge) {
        state.dailyChallenge.progress = Math.min(
          action.payload,
          state.dailyChallenge.target
        );
        
        if (state.dailyChallenge.progress >= state.dailyChallenge.target && !state.dailyChallenge.completed) {
          state.dailyChallenge.completed = true;
          state.totalPoints += state.dailyChallenge.reward;
          
          state.notifications.unshift({
            id: `challenge_${Date.now()}`,
            type: 'reward',
            title: 'Challenge Completed!',
            message: `${state.dailyChallenge.title} completed! Earned ${state.dailyChallenge.reward} points.`,
            timestamp: new Date().toISOString(),
            read: false,
          });
        }
      }
    },
    
    claimWeeklyReward: (state, action: PayloadAction<number>) => {
      const day = action.payload;
      if (day >= 0 && day < 7 && !state.weeklyRewards.claimed[day]) {
        state.weeklyRewards.claimed[day] = true;
        const reward = (day + 1) * 20; // Increasing rewards
        state.totalPoints += reward;
      }
    },
    
    updateReferralStats: (state, action: PayloadAction<{ referred?: number; earned?: number }>) => {
      if (action.payload.referred) {
        state.referralProgram.referredUsers += action.payload.referred;
        state.referralProgram.pendingRewards += action.payload.referred * 100; // 100 points per referral
      }
      if (action.payload.earned) {
        state.referralProgram.totalEarned += action.payload.earned;
        state.referralProgram.pendingRewards -= action.payload.earned;
        state.totalPoints += action.payload.earned;
      }
    },
    
    setReferralCode: (state, action: PayloadAction<string>) => {
      state.referralProgram.referralCode = action.payload;
    },
    
    markNotificationRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    
    clearAllNotifications: (state) => {
      state.notifications = [];
    },
    
    setSpinWheelAvailable: (state, action: PayloadAction<boolean>) => {
      state.spinWheel.available = action.payload;
      if (!action.payload) {
        state.spinWheel.lastSpinDate = new Date().toISOString();
      }
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(generateDailyChallenge.fulfilled, (state, action) => {
        state.dailyChallenge = action.payload;
      })
      .addCase(spinRewardWheel.pending, (state) => {
        state.loading = true;
      })
      .addCase(spinRewardWheel.fulfilled, (state, action) => {
        state.loading = false;
        const prize = action.payload;
        
        if (prize.type === 'points') {
          state.totalPoints += prize.value;
        }
        
        state.spinWheel.available = false;
        state.spinWheel.lastSpinDate = new Date().toISOString();
        
        state.notifications.unshift({
          id: `spin_${Date.now()}`,
          type: 'reward',
          title: 'Spin Wheel Reward!',
          message: `You won: ${prize.name}`,
          timestamp: new Date().toISOString(),
          read: false,
        });
      })
      .addCase(spinRewardWheel.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addXP,
  addPoints,
  unlockAchievement,
  updateAchievementProgress,
  updateStreak,
  addBadge,
  updateDailyChallengeProgress,
  claimWeeklyReward,
  updateReferralStats,
  setReferralCode,
  markNotificationRead,
  clearAllNotifications,
  setSpinWheelAvailable,
} = gamificationSlice.actions;

export default gamificationSlice.reducer;