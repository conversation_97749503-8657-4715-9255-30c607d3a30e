import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import ApiService from '../../services/api';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  images: string[];
  category: string;
  subcategory?: string;
  brand: string;
  unit: string;
  weight: string;
  ingredients?: string[];
  nutritionInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  allergens?: string[];
  isOrganic: boolean;
  isEcoFriendly: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  stockLevel: 'high' | 'medium' | 'low' | 'out_of_stock';
  stockCount?: number;
  rating: number;
  reviewCount: number;
  tags: string[];
  expiryDate?: string;
  manufacturingDate?: string;
  shelfLife?: string;
  storageInstructions?: string;
  popularity: number;
  trending: boolean;
  isNew: boolean;
  isBestSeller: boolean;
  recommendations?: {
    frequentlyBoughtTogether: string[];
    alternatives: string[];
    upgrades: string[];
  };
  priceHistory?: Array<{
    date: string;
    price: number;
  }>;
  availability: {
    deliveryTime: string;
    regions: string[];
  };
  socialProof: {
    purchasedToday: number;
    viewedToday: number;
    inCartsNow: number;
  };
  urgencyIndicators?: {
    limitedStock?: boolean;
    flashSale?: boolean;
    priceDrop?: boolean;
    trending?: boolean;
  };
}

interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  icon: string;
  color: string[];
  subcategories: Array<{
    id: string;
    name: string;
    productCount: number;
  }>;
  featuredProducts: string[];
  trending: boolean;
  offers?: {
    type: 'percentage' | 'fixed' | 'bogo';
    value: number;
    description: string;
  };
}

interface SearchFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  isOrganic?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isEcoFriendly?: boolean;
  inStock?: boolean;
  sortBy?: 'price_low' | 'price_high' | 'rating' | 'popularity' | 'newest' | 'discount';
  tags?: string[];
}

interface ProductsState {
  products: Product[];
  categories: Category[];
  featuredProducts: Product[];
  trendingProducts: Product[];
  newArrivals: Product[];
  bestSellers: Product[];
  recommendations: Product[];
  searchResults: Product[];
  searchQuery: string;
  searchFilters: SearchFilters;
  selectedCategory: Category | null;
  selectedProduct: Product | null;
  popularSearches: string[];
  recentSearches: string[];
  priceAlerts: Array<{
    productId: string;
    targetPrice: number;
    currentPrice: number;
  }>;
  wishlist: string[];
  recentlyViewed: string[];
  compareList: string[];
  bulkDiscounts: Array<{
    productId: string;
    minQuantity: number;
    discountPercentage: number;
  }>;
  flashSales: Array<{
    productId: string;
    originalPrice: number;
    salePrice: number;
    endTime: string;
    quantityLeft: number;
  }>;
  personalizedRecommendations: {
    basedOnHistory: Product[];
    trending: Product[];
    seasonal: Product[];
    onSale: Product[];
  };
  loading: {
    products: boolean;
    categories: boolean;
    search: boolean;
    recommendations: boolean;
  };
  error: string | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    hasMore: boolean;
    limit: number;
  };
}

const initialState: ProductsState = {
  products: [],
  categories: [],
  featuredProducts: [],
  trendingProducts: [],
  newArrivals: [],
  bestSellers: [],
  recommendations: [],
  searchResults: [],
  searchQuery: '',
  searchFilters: {},
  selectedCategory: null,
  selectedProduct: null,
  popularSearches: ['milk', 'bread', 'eggs', 'onions', 'tomatoes'],
  recentSearches: [],
  priceAlerts: [],
  wishlist: [],
  recentlyViewed: [],
  compareList: [],
  bulkDiscounts: [],
  flashSales: [],
  personalizedRecommendations: {
    basedOnHistory: [],
    trending: [],
    seasonal: [],
    onSale: [],
  },
  loading: {
    products: false,
    categories: false,
    search: false,
    recommendations: false,
  },
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    hasMore: false,
    limit: 20,
  },
};

// Async thunks
export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await ApiService.getCategories();
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch categories');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch categories');
    }
  }
);

export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async ({ categoryId, page = 1, filters }: { categoryId?: string; page?: number; filters?: SearchFilters }, { rejectWithValue }) => {
    try {
      const response = await ApiService.getProducts(categoryId);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch products');
      }
      return { data: response.data, page, categoryId };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch products');
    }
  }
);

export const searchProducts = createAsyncThunk(
  'products/searchProducts',
  async ({ query, filters }: { query: string; filters?: SearchFilters }, { rejectWithValue }) => {
    try {
      const response = await ApiService.searchProducts(query);
      if (!response.success) {
        return rejectWithValue(response.error || 'Search failed');
      }
      return { results: response.data, query };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Search failed');
    }
  }
);

export const fetchProductDetails = createAsyncThunk(
  'products/fetchProductDetails',
  async (productId: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.getProductDetails(productId);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch product details');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch product details');
    }
  }
);

export const fetchRecommendations = createAsyncThunk(
  'products/fetchRecommendations',
  async ({ type, productId }: { type: 'personalized' | 'similar' | 'trending'; productId?: string }, { rejectWithValue }) => {
    try {
      const endpoint = type === 'personalized' 
        ? '/products/recommendations/personalized'
        : type === 'similar' && productId
        ? `/products/recommendations/similar/${productId}`
        : '/products/recommendations/trending';
        
      const response = await ApiService.makeRequest(endpoint);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to fetch recommendations');
      }
      return { type, data: response.data };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch recommendations');
    }
  }
);

export const createPriceAlert = createAsyncThunk(
  'products/createPriceAlert',
  async ({ productId, targetPrice }: { productId: string; targetPrice: number }, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/products/price-alerts', {
        method: 'POST',
        body: JSON.stringify({ productId, targetPrice }),
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to create price alert');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to create price alert');
    }
  }
);

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      
      // Add to recent searches if not empty and not already present
      if (action.payload.trim() && !state.recentSearches.includes(action.payload)) {
        state.recentSearches.unshift(action.payload);
        state.recentSearches = state.recentSearches.slice(0, 10);
      }
    },

    setSearchFilters: (state, action: PayloadAction<SearchFilters>) => {
      state.searchFilters = { ...state.searchFilters, ...action.payload };
    },

    clearSearchFilters: (state) => {
      state.searchFilters = {};
    },

    setSelectedCategory: (state, action: PayloadAction<Category | null>) => {
      state.selectedCategory = action.payload;
    },

    setSelectedProduct: (state, action: PayloadAction<Product | null>) => {
      state.selectedProduct = action.payload;
      
      // Add to recently viewed
      if (action.payload && !state.recentlyViewed.includes(action.payload.id)) {
        state.recentlyViewed.unshift(action.payload.id);
        state.recentlyViewed = state.recentlyViewed.slice(0, 20);
      }
    },

    addToWishlist: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (!state.wishlist.includes(productId)) {
        state.wishlist.push(productId);
      }
    },

    removeFromWishlist: (state, action: PayloadAction<string>) => {
      state.wishlist = state.wishlist.filter(id => id !== action.payload);
    },

    addToCompare: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      if (!state.compareList.includes(productId) && state.compareList.length < 4) {
        state.compareList.push(productId);
      }
    },

    removeFromCompare: (state, action: PayloadAction<string>) => {
      state.compareList = state.compareList.filter(id => id !== action.payload);
    },

    clearCompareList: (state) => {
      state.compareList = [];
    },

    updateProductStock: (state, action: PayloadAction<{ productId: string; stockLevel: Product['stockLevel']; stockCount?: number }>) => {
      const { productId, stockLevel, stockCount } = action.payload;
      
      // Update in main products array
      const productIndex = state.products.findIndex(p => p.id === productId);
      if (productIndex !== -1) {
        state.products[productIndex].stockLevel = stockLevel;
        if (stockCount !== undefined) {
          state.products[productIndex].stockCount = stockCount;
        }
      }

      // Update in other arrays
      [state.featuredProducts, state.trendingProducts, state.newArrivals, state.bestSellers, state.searchResults].forEach(array => {
        const index = array.findIndex(p => p.id === productId);
        if (index !== -1) {
          array[index].stockLevel = stockLevel;
          if (stockCount !== undefined) {
            array[index].stockCount = stockCount;
          }
        }
      });
    },

    updateProductPrice: (state, action: PayloadAction<{ productId: string; newPrice: number; originalPrice?: number }>) => {
      const { productId, newPrice, originalPrice } = action.payload;
      
      const updatePrice = (product: Product) => {
        const oldPrice = product.price;
        product.price = newPrice;
        if (originalPrice) {
          product.originalPrice = originalPrice;
          product.discountPercentage = Math.round(((originalPrice - newPrice) / originalPrice) * 100);
        }
        
        // Add to price history
        if (!product.priceHistory) {
          product.priceHistory = [];
        }
        product.priceHistory.push({
          date: new Date().toISOString(),
          price: oldPrice,
        });
        
        // Keep only last 30 price entries
        product.priceHistory = product.priceHistory.slice(-30);
      };

      // Update in all arrays
      [state.products, state.featuredProducts, state.trendingProducts, state.newArrivals, state.bestSellers, state.searchResults].forEach(array => {
        const product = array.find(p => p.id === productId);
        if (product) {
          updatePrice(product);
        }
      });

      // Check price alerts
      state.priceAlerts.forEach(alert => {
        if (alert.productId === productId) {
          alert.currentPrice = newPrice;
        }
      });
    },

    addFlashSale: (state, action: PayloadAction<{ productId: string; salePrice: number; endTime: string; quantityLeft: number }>) => {
      const { productId, salePrice, endTime, quantityLeft } = action.payload;
      const product = state.products.find(p => p.id === productId);
      
      if (product) {
        state.flashSales.push({
          productId,
          originalPrice: product.price,
          salePrice,
          endTime,
          quantityLeft,
        });

        // Update product price
        product.originalPrice = product.price;
        product.price = salePrice;
        product.discountPercentage = Math.round(((product.originalPrice - salePrice) / product.originalPrice) * 100);
        
        if (!product.urgencyIndicators) {
          product.urgencyIndicators = {};
        }
        product.urgencyIndicators.flashSale = true;
      }
    },

    removeFlashSale: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      const flashSaleIndex = state.flashSales.findIndex(sale => sale.productId === productId);
      
      if (flashSaleIndex !== -1) {
        const flashSale = state.flashSales[flashSaleIndex];
        state.flashSales.splice(flashSaleIndex, 1);

        // Restore original price
        const product = state.products.find(p => p.id === productId);
        if (product) {
          product.price = flashSale.originalPrice;
          product.originalPrice = undefined;
          product.discountPercentage = undefined;
          
          if (product.urgencyIndicators) {
            product.urgencyIndicators.flashSale = false;
          }
        }
      }
    },

    updateSocialProof: (state, action: PayloadAction<{ productId: string; socialProof: Partial<Product['socialProof']> }>) => {
      const { productId, socialProof } = action.payload;
      
      [state.products, state.featuredProducts, state.trendingProducts, state.searchResults].forEach(array => {
        const product = array.find(p => p.id === productId);
        if (product) {
          product.socialProof = { ...product.socialProof, ...socialProof };
        }
      });
    },

    setTrendingStatus: (state, action: PayloadAction<{ productId: string; trending: boolean }>) => {
      const { productId, trending } = action.payload;
      
      [state.products, state.featuredProducts, state.searchResults].forEach(array => {
        const product = array.find(p => p.id === productId);
        if (product) {
          product.trending = trending;
          if (!product.urgencyIndicators) {
            product.urgencyIndicators = {};
          }
          product.urgencyIndicators.trending = trending;
        }
      });

      // Update trending products array
      if (trending) {
        const product = state.products.find(p => p.id === productId);
        if (product && !state.trendingProducts.find(p => p.id === productId)) {
          state.trendingProducts.push(product);
        }
      } else {
        state.trendingProducts = state.trendingProducts.filter(p => p.id !== productId);
      }
    },

    clearError: (state) => {
      state.error = null;
    },

    clearSearchResults: (state) => {
      state.searchResults = [];
      state.searchQuery = '';
    },

    setPagination: (state, action: PayloadAction<Partial<ProductsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },

  extraReducers: (builder) => {
    builder
      // Fetch Categories
      .addCase(fetchCategories.pending, (state) => {
        state.loading.categories = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.loading.categories = false;
        state.categories = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.loading.categories = false;
        state.error = action.payload as string;
      })

      // Fetch Products
      .addCase(fetchProducts.pending, (state) => {
        state.loading.products = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading.products = false;
        const { data, page, categoryId } = action.payload;
        
        if (page === 1) {
          state.products = data;
        } else {
          state.products.push(...data);
        }
        
        // Update category-specific arrays
        if (!categoryId) {
          state.featuredProducts = data.filter(p => p.isBestSeller).slice(0, 10);
          state.trendingProducts = data.filter(p => p.trending).slice(0, 10);
          state.newArrivals = data.filter(p => p.isNew).slice(0, 10);
          state.bestSellers = data.filter(p => p.isBestSeller).slice(0, 10);
        }
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading.products = false;
        state.error = action.payload as string;
      })

      // Search Products
      .addCase(searchProducts.pending, (state) => {
        state.loading.search = true;
        state.error = null;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.loading.search = false;
        state.searchResults = action.payload.results;
        state.searchQuery = action.payload.query;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.loading.search = false;
        state.error = action.payload as string;
      })

      // Fetch Product Details
      .addCase(fetchProductDetails.fulfilled, (state, action) => {
        state.selectedProduct = action.payload;
      })

      // Fetch Recommendations
      .addCase(fetchRecommendations.pending, (state) => {
        state.loading.recommendations = true;
      })
      .addCase(fetchRecommendations.fulfilled, (state, action) => {
        state.loading.recommendations = false;
        const { type, data } = action.payload;
        
        if (type === 'personalized') {
          state.personalizedRecommendations.basedOnHistory = data;
        } else if (type === 'trending') {
          state.personalizedRecommendations.trending = data;
        } else {
          state.recommendations = data;
        }
      })
      .addCase(fetchRecommendations.rejected, (state, action) => {
        state.loading.recommendations = false;
        state.error = action.payload as string;
      })

      // Create Price Alert
      .addCase(createPriceAlert.fulfilled, (state, action) => {
        state.priceAlerts.push(action.payload);
      });
  },
});

export const {
  setSearchQuery,
  setSearchFilters,
  clearSearchFilters,
  setSelectedCategory,
  setSelectedProduct,
  addToWishlist,
  removeFromWishlist,
  addToCompare,
  removeFromCompare,
  clearCompareList,
  updateProductStock,
  updateProductPrice,
  addFlashSale,
  removeFlashSale,
  updateSocialProof,
  setTrendingStatus,
  clearError,
  clearSearchResults,
  setPagination,
} = productsSlice.actions;

export default productsSlice.reducer;