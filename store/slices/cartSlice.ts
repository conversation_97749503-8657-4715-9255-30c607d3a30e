import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import ApiService from '../../services/api';

interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  quantity: number;
  maxQuantity: number;
  image: string;
  category: string;
  unit: string;
  brand: string;
  isEcoFriendly: boolean;
  isOrganic: boolean;
  expiryDate?: string;
  stockLevel: 'high' | 'medium' | 'low' | 'out_of_stock';
  lastPriceChange?: {
    oldPrice: number;
    newPrice: number;
    changeDate: string;
    type: 'increase' | 'decrease';
  };
  urgencyIndicator?: {
    type: 'limited_stock' | 'price_ending' | 'flash_sale' | 'trending';
    message: string;
    countdown?: number;
  };
}

interface DeliveryOption {
  id: string;
  name: string;
  price: number;
  estimatedTime: string;
  isEco: boolean;
  icon: string;
}

interface PricingBreakdown {
  subtotal: number;
  savings: number;
  deliveryFee: number;
  serviceFee: number;
  taxes: number;
  loyaltyDiscount: number;
  promoDiscount: number;
  total: number;
  youSave: number;
  pointsEarned: number;
}

interface CartRecommendation {
  id: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  reason: 'frequently_bought_together' | 'trending' | 'complementary' | 'bulk_discount';
  urgency?: string;
  discount?: number;
}

interface CartState {
  items: CartItem[];
  recommendations: CartRecommendation[];
  pricing: PricingBreakdown;
  selectedDeliveryOption: DeliveryOption | null;
  deliveryOptions: DeliveryOption[];
  appliedPromoCode: string | null;
  estimatedDeliveryTime: string;
  minimumOrderValue: number;
  freeDeliveryThreshold: number;
  bulkDiscountThreshold: number;
  cartValue: number;
  itemCount: number;
  cartAbandonment: {
    timestamp: string;
    reminderSent: boolean;
    savedForLater: CartItem[];
  } | null;
  recentlyViewed: Array<{
    productId: string;
    timestamp: string;
  }>;
  quickAdd: {
    recentItems: CartItem[];
    frequentItems: CartItem[];
  };
  socialProof: {
    trendingItems: string[];
    popularInArea: string[];
    friendsAlsoBought: string[];
  };
  urgencyTriggers: {
    stockAlert: boolean;
    priceAlert: boolean;
    deliverySlotAlert: boolean;
  };
  loading: boolean;
  error: string | null;
}

const initialDeliveryOptions: DeliveryOption[] = [
  {
    id: 'standard',
    name: 'Standard Delivery',
    price: 29,
    estimatedTime: '30-45 mins',
    isEco: false,
    icon: '🚚',
  },
  {
    id: 'express',
    name: 'Express Delivery',
    price: 49,
    estimatedTime: '15-30 mins',
    isEco: false,
    icon: '⚡',
  },
  {
    id: 'eco',
    name: 'Eco-Friendly',
    price: 25,
    estimatedTime: '45-60 mins',
    isEco: true,
    icon: '🌱',
  },
  {
    id: 'scheduled',
    name: 'Scheduled Delivery',
    price: 35,
    estimatedTime: 'Choose time slot',
    isEco: false,
    icon: '📅',
  },
];

const initialState: CartState = {
  items: [],
  recommendations: [],
  pricing: {
    subtotal: 0,
    savings: 0,
    deliveryFee: 29,
    serviceFee: 5,
    taxes: 0,
    loyaltyDiscount: 0,
    promoDiscount: 0,
    total: 0,
    youSave: 0,
    pointsEarned: 0,
  },
  selectedDeliveryOption: initialDeliveryOptions[0],
  deliveryOptions: initialDeliveryOptions,
  appliedPromoCode: null,
  estimatedDeliveryTime: '30-45 mins',
  minimumOrderValue: 99,
  freeDeliveryThreshold: 299,
  bulkDiscountThreshold: 499,
  cartValue: 0,
  itemCount: 0,
  cartAbandonment: null,
  recentlyViewed: [],
  quickAdd: {
    recentItems: [],
    frequentItems: [],
  },
  socialProof: {
    trendingItems: [],
    popularInArea: [],
    friendsAlsoBought: [],
  },
  urgencyTriggers: {
    stockAlert: false,
    priceAlert: false,
    deliverySlotAlert: false,
  },
  loading: false,
  error: null,
};

// Async thunks
export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async ({ productId, quantity }: { productId: string; quantity: number }, { rejectWithValue }) => {
    try {
      const response = await ApiService.addToCart(productId, quantity);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to add to cart');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to add to cart');
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateCartItem',
  async ({ itemId, quantity }: { itemId: string; quantity: number }, { rejectWithValue }) => {
    try {
      const response = await ApiService.updateCartItem(itemId, quantity);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to update cart');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update cart');
    }
  }
);

export const removeFromCart = createAsyncThunk(
  'cart/removeFromCart',
  async (itemId: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.removeFromCart(itemId);
      if (!response.success) {
        return rejectWithValue(response.error || 'Failed to remove from cart');
      }
      return itemId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to remove from cart');
    }
  }
);

export const applyPromoCode = createAsyncThunk(
  'cart/applyPromoCode',
  async (promoCode: string, { rejectWithValue }) => {
    try {
      const response = await ApiService.makeRequest('/cart/apply-promo', {
        method: 'POST',
        body: JSON.stringify({ promoCode }),
      });
      if (!response.success) {
        return rejectWithValue(response.error || 'Invalid promo code');
      }
      return response.data;
    } catch (error) {
      return rejectWithValue('Failed to apply promo code');
    }
  }
);

export const loadCartRecommendations = createAsyncThunk(
  'cart/loadRecommendations',
  async (_, { getState }) => {
    const state = getState() as { cart: CartState };
    const productIds = state.cart.items.map(item => item.productId);
    
    try {
      const response = await ApiService.makeRequest('/cart/recommendations', {
        method: 'POST',
        body: JSON.stringify({ productIds }),
      });
      return response.data || [];
    } catch (error) {
      return [];
    }
  }
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    calculatePricing: (state) => {
      const subtotal = state.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const savings = state.items.reduce((sum, item) => {
        if (item.originalPrice) {
          return sum + ((item.originalPrice - item.price) * item.quantity);
        }
        return sum;
      }, 0);

      state.pricing.subtotal = subtotal;
      state.pricing.savings = savings;
      state.cartValue = subtotal;
      state.itemCount = state.items.reduce((sum, item) => sum + item.quantity, 0);

      // Apply bulk discount
      if (subtotal >= state.bulkDiscountThreshold) {
        state.pricing.loyaltyDiscount = Math.floor(subtotal * 0.05); // 5% bulk discount
      }

      // Free delivery threshold
      if (subtotal >= state.freeDeliveryThreshold) {
        state.pricing.deliveryFee = 0;
      } else if (state.selectedDeliveryOption) {
        state.pricing.deliveryFee = state.selectedDeliveryOption.price;
      }

      // Calculate taxes (assuming 5% GST)
      state.pricing.taxes = Math.floor((subtotal + state.pricing.serviceFee) * 0.05);

      // Calculate total
      state.pricing.total = subtotal + 
        state.pricing.deliveryFee + 
        state.pricing.serviceFee + 
        state.pricing.taxes - 
        state.pricing.loyaltyDiscount - 
        state.pricing.promoDiscount;

      // Total savings
      state.pricing.youSave = savings + state.pricing.loyaltyDiscount + state.pricing.promoDiscount;

      // Points earned (1 point per ₹10 spent)
      state.pricing.pointsEarned = Math.floor(state.pricing.total / 10);
    },

    setDeliveryOption: (state, action: PayloadAction<DeliveryOption>) => {
      state.selectedDeliveryOption = action.payload;
      state.estimatedDeliveryTime = action.payload.estimatedTime;
    },

    addRecentlyViewed: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      const existing = state.recentlyViewed.findIndex(item => item.productId === productId);
      
      if (existing !== -1) {
        state.recentlyViewed.splice(existing, 1);
      }
      
      state.recentlyViewed.unshift({
        productId,
        timestamp: new Date().toISOString(),
      });

      // Keep only last 10 items
      state.recentlyViewed = state.recentlyViewed.slice(0, 10);
    },

    saveCartForLater: (state) => {
      state.cartAbandonment = {
        timestamp: new Date().toISOString(),
        reminderSent: false,
        savedForLater: [...state.items],
      };
    },

    restoreSavedCart: (state) => {
      if (state.cartAbandonment) {
        state.items = [...state.cartAbandonment.savedForLater];
        state.cartAbandonment = null;
      }
    },

    updateStockLevels: (state, action: PayloadAction<Array<{ productId: string; stockLevel: CartItem['stockLevel'] }>>) => {
      action.payload.forEach(({ productId, stockLevel }) => {
        const item = state.items.find(item => item.productId === productId);
        if (item) {
          item.stockLevel = stockLevel;
          
          // Add urgency indicators
          if (stockLevel === 'low') {
            item.urgencyIndicator = {
              type: 'limited_stock',
              message: 'Only few left in stock!',
            };
            state.urgencyTriggers.stockAlert = true;
          } else if (stockLevel === 'out_of_stock') {
            item.urgencyIndicator = {
              type: 'limited_stock',
              message: 'Out of stock - Remove from cart',
            };
          }
        }
      });
    },

    addPriceAlert: (state, action: PayloadAction<{ productId: string; oldPrice: number; newPrice: number }>) => {
      const { productId, oldPrice, newPrice } = action.payload;
      const item = state.items.find(item => item.productId === productId);
      
      if (item) {
        item.lastPriceChange = {
          oldPrice,
          newPrice,
          changeDate: new Date().toISOString(),
          type: newPrice > oldPrice ? 'increase' : 'decrease',
        };
        
        if (newPrice < oldPrice) {
          item.urgencyIndicator = {
            type: 'price_ending',
            message: `Price dropped by ₹${oldPrice - newPrice}!`,
          };
          state.urgencyTriggers.priceAlert = true;
        }
      }
    },

    addFlashSaleItem: (state, action: PayloadAction<{ productId: string; countdown: number }>) => {
      const { productId, countdown } = action.payload;
      const item = state.items.find(item => item.productId === productId);
      
      if (item) {
        item.urgencyIndicator = {
          type: 'flash_sale',
          message: 'Flash Sale - Limited Time!',
          countdown,
        };
      }
    },

    updateSocialProof: (state, action: PayloadAction<Partial<CartState['socialProof']>>) => {
      state.socialProof = { ...state.socialProof, ...action.payload };
    },

    clearCart: (state) => {
      state.items = [];
      state.appliedPromoCode = null;
      state.pricing.promoDiscount = 0;
    },

    clearError: (state) => {
      state.error = null;
    },

    setQuickAddItems: (state, action: PayloadAction<{ recent?: CartItem[]; frequent?: CartItem[] }>) => {
      if (action.payload.recent) {
        state.quickAdd.recentItems = action.payload.recent;
      }
      if (action.payload.frequent) {
        state.quickAdd.frequentItems = action.payload.frequent;
      }
    },

    addUrgencyTimer: (state, action: PayloadAction<{ type: 'delivery_slot'; message: string; countdown: number }>) => {
      state.urgencyTriggers.deliverySlotAlert = true;
    },
  },

  extraReducers: (builder) => {
    builder
      // Add to cart
      .addCase(addToCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.loading = false;
        const newItem = action.payload;
        const existingItem = state.items.find(item => item.productId === newItem.productId);
        
        if (existingItem) {
          existingItem.quantity += newItem.quantity;
        } else {
          state.items.push(newItem);
        }
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update cart item
      .addCase(updateCartItem.fulfilled, (state, action) => {
        const updatedItem = action.payload;
        const index = state.items.findIndex(item => item.id === updatedItem.id);
        if (index !== -1) {
          state.items[index] = updatedItem;
        }
      })

      // Remove from cart
      .addCase(removeFromCart.fulfilled, (state, action) => {
        const itemId = action.payload;
        state.items = state.items.filter(item => item.id !== itemId);
      })

      // Apply promo code
      .addCase(applyPromoCode.fulfilled, (state, action) => {
        state.appliedPromoCode = action.payload.code;
        state.pricing.promoDiscount = action.payload.discount;
      })
      .addCase(applyPromoCode.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // Load recommendations
      .addCase(loadCartRecommendations.fulfilled, (state, action) => {
        state.recommendations = action.payload;
      });
  },
});

export const {
  calculatePricing,
  setDeliveryOption,
  addRecentlyViewed,
  saveCartForLater,
  restoreSavedCart,
  updateStockLevels,
  addPriceAlert,
  addFlashSaleItem,
  updateSocialProof,
  clearCart,
  clearError,
  setQuickAddItems,
  addUrgencyTimer,
} = cartSlice.actions;

export default cartSlice.reducer;