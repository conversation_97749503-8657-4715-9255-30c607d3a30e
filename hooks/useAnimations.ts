import { useEffect, useRef } from 'react';
import { Animated, Easing, Platform } from 'react-native';

// Check if running on web to conditionally set useNativeDriver
const IS_WEB = Platform.OS === 'web';

export const useAnimations = () => {
  // Scale animation for press effects
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  
  // Opacity animation for fade effects
  const opacityAnimation = useRef(new Animated.Value(1)).current;
  
  // Slide up animation
  const slideUpAnimation = useRef(new Animated.Value(30)).current;
  
  // Animation for search bar focus
  const searchBarWidth = useRef(new Animated.Value(100)).current;
  
  // Press animation for touchable elements
  const animatePress = (callback?: () => void) => {
    Animated.sequence([
      Animated.timing(scaleAnimation, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: !IS_WEB, // Use native driver except on web
        easing: Easing.inOut(Easing.ease),
      }),
      Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: 100,
        useNativeDriver: !IS_WEB,
        easing: Easing.inOut(Easing.ease),
      }),
    ]).start(() => {
      if (callback) callback();
    });
  };
  
  // Fade in animation
  const fadeIn = (duration = 300) => {
    Animated.timing(opacityAnimation, {
      toValue: 1,
      duration,
      useNativeDriver: !IS_WEB,
      easing: Easing.inOut(Easing.ease),
    }).start();
  };
  
  // Fade out animation
  const fadeOut = (duration = 300) => {
    Animated.timing(opacityAnimation, {
      toValue: 0,
      duration,
      useNativeDriver: !IS_WEB,
      easing: Easing.inOut(Easing.ease),
    }).start();
  };
  
  // Slide up animation for elements
  const slideUp = (duration = 400) => {
    Animated.timing(slideUpAnimation, {
      toValue: 0,
      duration,
      useNativeDriver: !IS_WEB,
      easing: Easing.out(Easing.ease),
    }).start();
  };

  // Animation for search bar focus
  const expandSearchBar = (expanded: boolean, maxWidth: number) => {
    Animated.timing(searchBarWidth, {
      toValue: expanded ? maxWidth : 100,
      duration: 300,
      useNativeDriver: false, // layout animations can't use native driver
      easing: Easing.inOut(Easing.ease),
    }).start();
  };
  
  // Initialize animations
  useEffect(() => {
    // Reset animation values to initial state
    scaleAnimation.setValue(1);
    opacityAnimation.setValue(1);
    slideUpAnimation.setValue(30);
    
    // Run initial animations if needed
    slideUp();
    fadeIn();
  }, []);
  
  return {
    scaleAnimation,
    opacityAnimation,
    slideUpAnimation,
    searchBarWidth,
    animatePress,
    fadeIn,
    fadeOut,
    slideUp,
    expandSearchBar,
  };
};

export default useAnimations; 