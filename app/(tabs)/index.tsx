import { useNavigation } from 'expo-router';
import React, { memo, useCallback, useRef, useState, useEffect } from 'react';
import { 
  Animated, 
  Platform, 
  StatusBar, 
  StyleSheet, 
  View, 
  ScrollView, 
  Text, 
  TouchableOpacity, 
  RefreshControl,
  Dimensions,
  Alert
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as Animatable from 'react-native-animatable';

import CategoryGrid, { Category } from '@/components/CategoryGrid';
import Header from '@/components/Header';
import SearchBar from '@/components/SearchBar';
import SpecialOffers, { Offer } from '@/components/SpecialOffers';
import UrgencyIndicator from '@/components/advanced/UrgencyIndicator';
import SocialProof from '@/components/advanced/SocialProof';
import GamificationProgress from '@/components/advanced/GamificationProgress';
import Colors from '@/constants/Colors';
import useAnimations from '@/hooks/useAnimations';
import { TabScreenProps } from '@/types/navigation';

const { width, height } = Dimensions.get('window');
const IS_WEB = Platform.OS === 'web';

interface QuickAction {
  id: string;
  title: string;
  icon: string;
  color: string[];
  action: () => void;
}

interface LiveUpdate {
  id: string;
  type: 'order' | 'delivery' | 'offer';
  message: string;
  timestamp: Date;
}

const HomeScreen = () => {
  const navigation = useNavigation<TabScreenProps<'index'>['navigation']>();
  const [searchFocused, setSearchFocused] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [liveUpdates, setLiveUpdates] = useState<LiveUpdate[]>([]);
  const [showFloatingActions, setShowFloatingActions] = useState(false);
  const [userStreak, setUserStreak] = useState(3);
  const [dailyProgress, setDailyProgress] = useState(0.7);

  const scrollY = useRef(new Animated.Value(0)).current;
  const floatingButtonAnim = useRef(new Animated.Value(0)).current;
  const { slideUpAnimation, opacityAnimation } = useAnimations();

  // Mock user data - replace with Redux selectors
  const userLevel = {
    currentLevel: 5,
    currentXP: 850,
    xpToNextLevel: 1000,
    levelName: 'Smart Shopper',
    nextLevelName: 'Bulk Buyer',
    perks: ['5% bulk discount', 'Priority support']
  };

  const userPoints = {
    current: 2450,
    earned: 50,
    redeemable: 2450,
    nextReward: 3000
  };

  const dailyChallenge = {
    title: 'Green Day',
    description: 'Buy 3 organic products',
    progress: 2,
    target: 3,
    reward: 100,
    timeLeft: 18000, // 5 hours
    completed: false
  };

  const recentActivity = {
    count: 47,
    timeframe: 'last hour',
    action: 'orders placed'
  };

  const trendingProduct = {
    rank: 1,
    category: 'Vegetables',
    growth: 150
  };

  const quickActions: QuickAction[] = [
    {
      id: '1',
      title: 'Voice Order',
      icon: 'microphone',
      color: [Colors.psychology.energy, Colors.primary.end],
      action: () => handleVoiceOrder(),
    },
    {
      id: '2',
      title: 'Scan Barcode',
      icon: 'qrcode',
      color: [Colors.secondary.start, Colors.secondary.end],
      action: () => handleBarcodeScanner(),
    },
    {
      id: '3',
      title: 'Quick Reorder',
      icon: 'repeat',
      color: [Colors.accent.purple, Colors.accent.teal],
      action: () => handleQuickReorder(),
    },
    {
      id: '4',
      title: 'Share & Earn',
      icon: 'share',
      color: [Colors.social.friends, Colors.social.verified],
      action: () => handleShareAndEarn(),
    },
  ];

  useEffect(() => {
    // Update time every minute for fresh feeling
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    // Simulate live updates
    const updateTimer = setInterval(() => {
      const updates = [
        { id: Date.now().toString(), type: 'order' as const, message: 'John just ordered fresh mangoes!', timestamp: new Date() },
        { id: (Date.now() + 1).toString(), type: 'delivery' as const, message: 'Express delivery available in your area', timestamp: new Date() },
        { id: (Date.now() + 2).toString(), type: 'offer' as const, message: 'Flash sale: 30% off on dairy products!', timestamp: new Date() },
      ];
      setLiveUpdates(prev => [...updates, ...prev].slice(0, 5));
    }, 30000);

    return () => {
      clearInterval(timer);
      clearInterval(updateTimer);
    };
  }, []);

  useEffect(() => {
    // Floating action button animation
    Animated.spring(floatingButtonAnim, {
      toValue: showFloatingActions ? 1 : 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [showFloatingActions]);

  const handleCategoryPress = useCallback((category: Category) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('category-products', {
      categoryId: category.id,
      categoryName: category.name,
    });
  }, [navigation]);

  const handleOfferPress = useCallback((offer: Offer) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log('Offer pressed:', offer.title);
  }, []);

  const handleSearchPress = useCallback(() => {
    setSearchFocused(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const timeout = setTimeout(() => {
      navigation.navigate('search');
      const resetTimeout = setTimeout(() => setSearchFocused(false), 500);
      return () => clearTimeout(resetTimeout);
    }, 300);
    
    return () => clearTimeout(timeout);
  }, [navigation]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
      setUserStreak(prev => prev + 1);
      setDailyProgress(prev => Math.min(prev + 0.1, 1));
    }, 1500);
  }, []);

  const handleVoiceOrder = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    Alert.alert('Voice Order', 'Say what you need and we\'ll add it to your cart!');
  }, []);

  const handleBarcodeScanner = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    Alert.alert('Barcode Scanner', 'Scan any product to add it instantly!');
  }, []);

  const handleQuickReorder = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    Alert.alert('Quick Reorder', 'Reordering your last 5 items...');
  }, []);

  const handleShareAndEarn = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    Alert.alert('Share & Earn', 'Share with friends and earn ₹100 for each referral!');
  }, []);

  const handleScroll = useCallback(
    Animated.event(
      [{ nativeEvent: { contentOffset: { y: scrollY } } }],
      { 
        useNativeDriver: !IS_WEB,
        listener: (event: any) => {
          const offsetY = event.nativeEvent.contentOffset.y;
          setShowFloatingActions(offsetY > 200);
        }
      }
    ),
    [scrollY]
  );

  // Header animations
  const headerScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.95],
    extrapolate: 'clamp',
  });

  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -10],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 60, 100],
    outputRange: [1, 1, 0.95],
    extrapolate: 'clamp',
  });

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '🌅 Good Morning';
    if (hour < 17) return '☀️ Good Afternoon';
    return '🌙 Good Evening';
  };

  const renderFloatingActions = () => (
    <Animated.View
      style={[
        styles.floatingContainer,
        {
          transform: [
            {
              translateY: floatingButtonAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [200, 0],
              }),
            },
          ],
          opacity: floatingButtonAnim,
        },
      ]}
    >
      {quickActions.map((action, index) => (
        <Animatable.View
          key={action.id}
          animation="bounceIn"
          delay={index * 100}
          style={styles.floatingAction}
        >
          <TouchableOpacity
            onPress={action.action}
            style={styles.floatingButton}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={action.color}
              style={styles.floatingGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <FontAwesome name={action.icon as any} size={16} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
          <Text style={styles.floatingLabel}>{action.title}</Text>
        </Animatable.View>
      ))}
    </Animated.View>
  );

  const renderLiveUpdates = () => (
    <View style={styles.liveUpdatesContainer}>
      <Text style={styles.liveUpdatesTitle}>🔴 Live Activity</Text>
      {liveUpdates.slice(0, 3).map((update) => (
        <Animatable.View
          key={update.id}
          animation="slideInRight"
          duration={500}
          style={styles.liveUpdate}
        >
          <Text style={styles.liveUpdateText}>{update.message}</Text>
        </Animatable.View>
      ))}
    </View>
  );

  const renderPersonalizedSection = () => (
    <View style={styles.personalizedContainer}>
      <View style={styles.personalizedHeader}>
        <Text style={styles.greeting}>{getGreeting()}</Text>
        <View style={styles.streakContainer}>
          <FontAwesome name="fire" size={16} color={Colors.gamification.streak} />
          <Text style={styles.streakText}>{userStreak} day streak!</Text>
        </View>
      </View>
      
      <GamificationProgress
        type="level"
        data={userLevel}
        style={styles.gamificationCard}
      />
      
      <View style={styles.quickStatsRow}>
        <GamificationProgress
          type="points"
          data={userPoints}
          style={[styles.gamificationCard, styles.halfWidth]}
        />
        <GamificationProgress
          type="daily_challenge"
          data={dailyChallenge}
          style={[styles.gamificationCard, styles.halfWidth]}
        />
      </View>
    </View>
  );

  const renderUrgencySection = () => (
    <View style={styles.urgencyContainer}>
      <UrgencyIndicator
        type="stock"
        message="Limited stock alert!"
        intensity="high"
        stockCount={3}
        style={styles.urgencyItem}
      />
      <UrgencyIndicator
        type="time"
        message="Flash sale ending soon!"
        countdown={3600}
        intensity="high"
        style={styles.urgencyItem}
      />
      <UrgencyIndicator
        type="demand"
        message="High demand in your area"
        intensity="medium"
        style={styles.urgencyItem}
      />
    </View>
  );

  const renderSocialProofSection = () => (
    <View style={styles.socialProofContainer}>
      <SocialProof
        type="activity"
        data={recentActivity}
        style={styles.socialProofItem}
      />
      <SocialProof
        type="trending"
        data={trendingProduct}
        style={styles.socialProofItem}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.start} />
      
      {/* Animated Header */}
      <Animated.View 
        style={[
          styles.headerContainer, 
          { 
            transform: [
              { scale: headerScale },
              { translateY: headerTranslateY }
            ],
            opacity: headerOpacity
          }
        ]}
      >
        <Header location="Home • Sector 62, Noida" />
      </Animated.View>
      
      {/* Search Bar */}
      <SearchBar onPress={handleSearchPress} focused={searchFocused} />
      
      {/* Main Content */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        removeClippedSubviews={true}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary.start]}
            tintColor={Colors.primary.start}
          />
        }
      >
        {/* Personalized Gamification Section */}
        {renderPersonalizedSection()}
        
        {/* Live Updates */}
        {renderLiveUpdates()}
        
        {/* Urgency Indicators */}
        {renderUrgencySection()}
        
        {/* Social Proof */}
        {renderSocialProofSection()}
        
        {/* Categories */}
        <CategoryGrid onCategoryPress={handleCategoryPress} />
        
        {/* Special Offers */}
        <SpecialOffers onOfferPress={handleOfferPress} />
        
        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
      
      {/* Floating Quick Actions */}
      {renderFloatingActions()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  headerContainer: {
    zIndex: 10,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 100,
  },
  
  // Personalized Section
  personalizedContainer: {
    padding: 16,
    backgroundColor: Colors.background.primary,
  },
  personalizedHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  greeting: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 87, 34, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  streakText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.gamification.streak,
    marginLeft: 4,
  },
  gamificationCard: {
    marginBottom: 12,
  },
  quickStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
    marginBottom: 0,
  },
  
  // Live Updates
  liveUpdatesContainer: {
    backgroundColor: 'rgba(255, 107, 53, 0.05)',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.social.trending,
  },
  liveUpdatesTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  liveUpdate: {
    backgroundColor: Colors.background.card,
    padding: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  liveUpdateText: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  
  // Urgency Section
  urgencyContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  urgencyItem: {
    marginBottom: 6,
  },
  
  // Social Proof Section
  socialProofContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  socialProofItem: {
    marginBottom: 8,
  },
  
  // Floating Actions
  floatingContainer: {
    position: 'absolute',
    right: 16,
    bottom: 100,
    alignItems: 'center',
  },
  floatingAction: {
    alignItems: 'center',
    marginBottom: 12,
  },
  floatingButton: {
    borderRadius: 25,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: Colors.shadow.medium,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  floatingGradient: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatingLabel: {
    fontSize: 10,
    color: Colors.text.secondary,
    marginTop: 4,
    textAlign: 'center',
    maxWidth: 60,
  },
  
  // Bottom spacing
  bottomSpacing: {
    height: 20,
  },
});

export default memo(HomeScreen);